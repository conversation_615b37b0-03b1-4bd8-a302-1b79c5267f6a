���      �pandas.core.series��Series���)��}�(�_mgr��pandas.core.internals.managers��SingleBlockManager���)��(]��pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�	sourceKey��&id��traderFileIdentifier��&key��;instrumentDetails.instrument.ext.instrumentUniqueIdentifier��orderIdentifiers.orderIdCode��-instrumentDetails.instrument.instrumentIdCode��step_id��reason��!orderIdentifiers.transactionRefNo��clientFileIdentifier��HtradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier�et�b�name�Nu��R�a]�hhK ��h��R�(KK��h!�]�(�fs3://mar.dev.steeleye.co/flows/order-universal-steeleye-trade-blotter/seBlotter.CH.CT.********.csv.pgp��**********:1:NEWO��account:9446799��%Order:**********:1:NEWO:*************��%OPRACA0679011084OP2023-04-06 00:00:00��
**********�G�      �ZbnbFEMHO5y9WrkeywKeV��Orders not in the time window�NNNet�ba]�h
h}�(hhhK ��h��R�(KK��h!�]�(h%h&h'h(h)h*h+h,h-h.h/h0et�bh2Nu��R�a}��0.14.1�}�(�axes�h
�blocks�]�}�(�values�h8�mgr_locs��builtins��slice���K KK��R�uaust�b�_typ��series��	_metadata�]�h2a�attrs�}��_flags�}��allows_duplicate_labels��sh2Nub.