# mypy: disable-error-code="attr-defined"
# ruff: noqa: E501
import boto3
import pandas as pd
from addict import addict
from aries_config_cached_client.tenant_workflow import CachedTenantWorkflowAPIClient
from aries_se_comms_tasks.generic.participants import link_participants
from aries_se_comms_tasks.generic.voice.call_duration_from_attachment import (
    CallDurationFromAttachment,
)
from aries_se_core_tasks.core.generic_app_metrics_enum import GenericAppMetricsEnum
from aries_se_core_tasks.io.read.cloud.download_file import run_download_file
from aries_se_core_tasks.utilities.helpers_for_tests import sort_identifiers_columns  # type: ignore
from aries_task_link.models import AriesTaskInput
from freezegun import freeze_time
from integration_audio_comms_tasks.voice.zoom_trium_voice_transform.zoom_trium_voice_transform_task import (
    zoom_trium_voice_transform_run,
)
from integration_test_utils.aws_helpers.patch_response import (
    mock_aiobotocore_convert_to_response_dict,
)
from integration_test_utils.model_validation.validation_errors import (
    assert_record_is_schema_compliant,
)
from integration_wrapper.static import Static<PERSON>ields
from moto import mock_aws
from pathlib import Path
from se_elastic_schema.models import Call
from se_elastic_schema.steeleye_record_validations.error_codes import SteeleyeRecordErrorCodesEnum
from se_elasticsearch.repository.models import ResourceConfig
from se_elasticsearch.repository.static import MetaPrefix
from se_enums.elastic_search import EsActionEnum
from typing import List
from unittest.mock import patch

BUCKET_NAME: str = "test.dev.steeleye.co"

CURRENT_PATH = Path(__file__).parent
DATA_PATH = CURRENT_PATH.joinpath("data")
LOCAL_BUCKET_PATH = DATA_PATH.joinpath("buckets", BUCKET_NAME)

EXPECTED_RESULTS_PATH = DATA_PATH.joinpath("expected_results")
EXPECTED_OUTPUT_NDJSON_PATH = EXPECTED_RESULTS_PATH.joinpath("expected_output.ndjson")


mock_aiobotocore_convert_to_response_dict()


class TestZoomTriumVoiceTransform:
    """Test suite for Zoom Trium Voice Transform."""

    @mock_aws
    @freeze_time(time_to_freeze="2024-05-20 06:59:38.911459+00:00")
    def test_valid_executions(
        self,
        sample_aries_task_input: AriesTaskInput,
        link_participants_scroll_result: List[dict],
    ):
        """Tests valid executions The test is done by adding files into a mock
        bucket Calls _run_aries_task internally, which actually adds the files
        to the mock bucket and patches functions which read from Elastic.

        :param sample_aries_task_input: Aries task input
        :param link_participants_scroll_result: Fixture which mocks the results returned
        by the scroll in the LinkParticipants task
        :return:
        """
        # Create mock S3 bucket and objects to it
        create_and_add_objects_to_s3_bucket(bucket_name=BUCKET_NAME)

        aries_task_result = self._run_aries_task(  # type: ignore
            sample_aries_task_input=sample_aries_task_input,
            link_participants_scroll_result=link_participants_scroll_result,  # type: ignore
        )

        ndjson_s3_uri: str = (
            aries_task_result.output_param.params.get("Call").get("params").get("file_uri")
        )

        waveform_s3_uri: str = (
            aries_task_result.output_param.params.get("Waveform").get("params").get("file_uri")
        )

        local_file_path: str = run_download_file(
            file_url=ndjson_s3_uri,
        )

        final_result_expected: pd.DataFrame = pd.read_json(
            EXPECTED_OUTPUT_NDJSON_PATH.as_posix(), lines=True
        )

        final_result: pd.DataFrame = pd.read_json(local_file_path, lines=True)

        # Sort identifiers columns
        sort_identifiers_columns(result_df=final_result, expected_result_df=final_result_expected)

        # As there is a chance that when the zip file is converted to S3Files,
        # the order might be different. So, we need to sort
        # the rows for this test, and drop the sourceIndex, reset index
        pd.testing.assert_frame_equal(
            final_result.sort_values(by="id").drop(["sourceIndex"], axis=1).reset_index(drop=True),
            final_result_expected.sort_values(by="id")
            .drop(["sourceIndex"], axis=1)
            .reset_index(drop=True),
        )

        metrics = (
            aries_task_result.app_metric.metrics[StaticFields.DATA_INTEGRATION_METRICS_PREFIX][
                "zoom_trium_voice"
            ]["zoom_trium_voice_transform"]
            if aries_task_result.app_metric
            else {}
        )
        assert metrics[GenericAppMetricsEnum.OUTPUT_COUNT] == 4

        assert aries_task_result.output_param.params == {
            "Call": {
                "params": {
                    "file_uri": ndjson_s3_uri,
                    "es_action": EsActionEnum.INDEX.value,
                    "data_model": "se_elastic_schema.models.tenant.communication.call:Call",
                }
            },
            "Waveform": {
                "params": {
                    "file_uri": waveform_s3_uri,
                }
            },
        }

        assert_record_is_schema_compliant(
            input_df=final_result,
            model=Call,
            accepted_validation_error_codes=[
                SteeleyeRecordErrorCodesEnum.SE_DV_531,  # To ID participant linking not done
                SteeleyeRecordErrorCodesEnum.SE_DV_547,  # The identifier for From ID is in a non-compliant format in source data
            ],
        )

    @staticmethod
    @patch.object(CallDurationFromAttachment, "_get_duration_from_downloaded_cloud_file")
    @patch.object(link_participants, "get_repository_by_cluster_version")
    @patch.object(link_participants, "get_es_config")
    @patch.object(
        target=CachedTenantWorkflowAPIClient,
        attribute="get",
        return_value=addict.Dict(
            {
                "tenant": {
                    "lake_prefix": f"s3://{BUCKET_NAME}",
                },
                "workflow": {"streamed": True},
            },
        ),
    )
    def _run_aries_task(
        _mock_cached_tenant_workflow_api_client,
        mock_link_participants_get_es_config,
        mock_link_participants_elasticsearch_repository,
        mock_duration_fetch_from_cloud,
        link_participants_scroll_result: dict,
        sample_aries_task_input: AriesTaskInput,
    ):
        """Runs the flow after uploading the input files to mock S3 and mocking
        functions which read from Elasticsearch.

        :param mock_link_participants_get_es_config: Unittest patch object
        :param mock_link_participants_elasticsearch_repository: Unittest patch object
        :param link_participants_scroll_result: Mock LinkParticipants scroll result
        :return:
        """
        aries_task_input = sample_aries_task_input

        # Mock Get call duration computation
        mock_duration_fetch_from_cloud.side_effect = ["1:20:30", "00:00:55", "00:00:23", "00:00:42"]

        # Mocks for link_participants elastic config
        mock_link_participants_get_es_config.return_value = ResourceConfig(
            host="localhost",
            port=9200,
            scheme="http",
            meta_prefix=MetaPrefix.AMPERSAND,
        )

        # Mocks link participants elastic repository
        es_obj = mock_link_participants_elasticsearch_repository.return_value
        es_obj.scroll.return_value = link_participants_scroll_result
        es_obj.MAX_TERMS_SIZE = 1024
        es_obj.meta.prefix = MetaPrefix.AMPERSAND
        es_obj.meta.key = "&key"
        es_obj.meta.id = "&id"

        # Run flow
        return zoom_trium_voice_transform_run(aries_task_input=aries_task_input)


def create_and_add_objects_to_s3_bucket(bucket_name: str):
    """Recreate the s3 bucket, and copy all the files from `LOCAL_BUCKET_PATH`
    to s3 mocked bucket.

    :param bucket_name: Bucket name of Mock S3 bucket
    :return: None
    """

    # Create bucket
    s3 = boto3.client("s3", region_name="us-east-1")
    s3.create_bucket(Bucket=bucket_name)

    for file_ in LOCAL_BUCKET_PATH.rglob("*"):
        if file_.is_file():
            _path = file_.as_posix().replace(f"{LOCAL_BUCKET_PATH}/", "")

            with open(file_, "rb") as f:
                s3.put_object(Bucket=bucket_name, Key=_path, Body=f.read())
