import boto3
import numpy as np
import pandas as pd
import pytest
from addict import addict
from aries_config_cached_client.tenant_workflow import CachedTenantWorkflowAPIClient
from aries_se_comms_tasks.generic.participants import link_participants
from aries_se_core_tasks.core.generic_app_metrics_enum import GenericAppMetricsEnum
from aries_se_core_tasks.io.read.cloud.download_file import run_download_file
from aries_se_core_tasks.io.utils import check_file_exists
from aries_se_core_tasks.utilities.helpers_for_tests import sort_identifiers_columns  # type: ignore
from aries_task_link.models import AriesTaskInput
from freezegun import freeze_time
from integration_audio_comms_tasks.voice.cloud9_voice_transform.cloud9_voice_transform_task import (
    cloud9_voice_transform_run,
)
from integration_test_utils.aws_helpers.patch_response import (
    mock_aiobotocore_convert_to_response_dict,
)
from integration_test_utils.model_validation.validation_errors import (
    assert_record_is_schema_compliant,
)
from integration_wrapper.static import StaticFields
from moto import mock_aws
from pathlib import Path
from se_data_lake.cloud_utils import get_bucket
from se_elastic_schema.models import Call
from se_elastic_schema.steeleye_record_validations.error_codes import SteeleyeRecordErrorCodesEnum
from se_elasticsearch.repository.models import ResourceConfig
from se_elasticsearch.repository.static import MetaPrefix
from se_enums.elastic_search import EsActionEnum
from unittest.mock import patch

CURRENT_PATH = Path(__file__).parent
LOCAL_BUCKET_PATH = CURRENT_PATH.joinpath("data/buckets")

DATA_PATH = Path(__file__).parent.joinpath("data")
EXPECTED_RESULTS_PATH = DATA_PATH.joinpath("expected_results")
EXPECTED_OUTPUT_NDJSON_PATH = EXPECTED_RESULTS_PATH.joinpath("expected_output.ndjson")
EXPECTED_OUTPUT_SHOUTDOWN_NDJSON_PATH = EXPECTED_RESULTS_PATH.joinpath(
    "expected_output_shoutdown_calls.ndjson"
)
EXPECTED_OUTPUT_ARROW_OVERRIDE_NDJSON_PATH = EXPECTED_RESULTS_PATH.joinpath(
    "expected_output_arrow_override_path.ndjson"
)

mock_aiobotocore_convert_to_response_dict()


class TestCloud9VoiceTransform:
    """Test suite for Cloud9 Voice Transform."""

    @pytest.mark.parametrize(
        "output_path, file_uri, tenant, expected_result_count, accepted_validation_error_codes",
        [
            # (
            #     EXPECTED_OUTPUT_NDJSON_PATH,
            #     "s3://test.dev.steeleye.co/aries/ingress/nonstreamed/evented/cloud9_voice_poll/2024/05/16/random_trace_id/cDt-20230310-100940-simaujla1-7242272-1.json",
            #     "test",
            #     2,
            #     [
            #         SteeleyeRecordErrorCodesEnum.SE_DV_522,
            #         SteeleyeRecordErrorCodesEnum.SE_DV_547,
            #         SteeleyeRecordErrorCodesEnum.SE_DV_551,
            #     ],
            # ),
            (
                EXPECTED_OUTPUT_ARROW_OVERRIDE_NDJSON_PATH,
                "s3://arrow.dev.steeleye.co/aries/ingress/nonstreamed/evented/cloud9_voice_poll/2024/11/7/random_trace_id/20240818_3QrCFwX2JL.json",
                "arrow",
                1,
                [
                    SteeleyeRecordErrorCodesEnum.SE_DV_551,
                    SteeleyeRecordErrorCodesEnum.SE_DV_521,
                    SteeleyeRecordErrorCodesEnum.SE_DV_522,
                ],
            ),
        ],
    )
    @mock_aws
    def test_it_can_run_end_to_end(
        self,
        sample_aries_task_input: AriesTaskInput,
        output_path: str,
        file_uri: str,
        tenant: str,
        expected_result_count,
        accepted_validation_error_codes,
        link_participants_scroll_result: pd.DataFrame,
        mocker,
    ):
        """Test it the flow execution end to end."""

        bucket_name = get_bucket(file_uri=file_uri)
        sample_aries_task_input.input_param.params["file_uri"] = file_uri
        sample_aries_task_input.workflow.tenant = tenant

        with freeze_time(time_to_freeze="2022-07-06 06:59:38.911459+00:00"):
            # Create mock S3 bucket and objects to it
            create_and_add_objects_to_s3_bucket(bucket_name=bucket_name)

            aries_task_result = self._run_aries_task(  # type: ignore
                sample_aries_task_input=sample_aries_task_input,
                link_participants_scroll_result=link_participants_scroll_result,
                bucket_name=bucket_name,
                mocker=mocker,
            )

        ndjon_s3_uri: str = (
            aries_task_result.output_param.params.get("Call").get("params").get("file_uri")
        )

        waveform_s3_uri: str = (
            aries_task_result.output_param.params.get("Waveform").get("params").get("file_uri")
        )

        local_file_path: str = run_download_file(
            file_url=ndjon_s3_uri,
        )

        final_result_expected: pd.DataFrame = pd.read_json(output_path, lines=True)

        final_result: pd.DataFrame = pd.read_json(local_file_path, lines=True)

        # Drop sizeInBytes and contentLength columns as they are compared above
        final_result["voiceFile"] = final_result["voiceFile"].apply(
            lambda x: TestCloud9VoiceTransform.drop_size_in_bytes_and_content_length_columns(x)
        )

        final_result_expected["voiceFile"] = final_result_expected["voiceFile"].apply(
            lambda x: TestCloud9VoiceTransform.drop_size_in_bytes_and_content_length_columns(x)
        )

        # Sort identifiers columns
        sort_identifiers_columns(result_df=final_result, expected_result_df=final_result_expected)

        pd.testing.assert_frame_equal(
            left=final_result.sort_values(by="id")
            .drop(["sourceIndex"], axis=1)
            .reset_index(drop=True),
            right=final_result_expected.sort_values(by="id")
            .drop(["sourceIndex"], axis=1)
            .reset_index(drop=True),
            check_like=True,
        )

        # Recipient does not have a link by participant in the second call
        # Sender ID's format is a bit wonky
        assert_record_is_schema_compliant(
            input_df=final_result,
            model=Call,
            accepted_validation_error_codes=accepted_validation_error_codes,
        )

        metrics = (
            aries_task_result.app_metric.metrics[StaticFields.DATA_INTEGRATION_METRICS_PREFIX][
                "cloud9_voice"
            ]["cloud9_voice_transform"]
            if aries_task_result.app_metric
            else {}
        )

        assert metrics[GenericAppMetricsEnum.OUTPUT_COUNT] == expected_result_count

        assert aries_task_result.output_param.params == {
            "Call": {
                "params": {
                    "file_uri": ndjon_s3_uri,
                    "es_action": EsActionEnum.INDEX.value,
                    "data_model": "se_elastic_schema.models.tenant.communication.call:Call",
                }
            },
            "Waveform": {
                "params": {
                    "file_uri": waveform_s3_uri,
                }
            },
        }

    # @mock_aws
    # def test_it_can_handle_shoutdown_calls(
    #     self,
    #     sample_aries_task_input: AriesTaskInput,
    #     link_participants_scroll_result: pd.DataFrame,
    #     caplog,
    #     mocker,
    # ):
    #     """This test ensures:
    #
    #     - calls are correctly split into segments when offset exists
    #     and the segments are correctly uploaded to correct S3 path
    #     - calls with negative offset are skipped and logged
    #     - can handle calls without the recording present
    #     """
    #     with freeze_time(time_to_freeze="2022-07-06 06:59:38.911459+00:00"):
    #         # Create mock S3 bucket and objects to it
    #
    #         sample_aries_task_input.input_param.params["file_uri"] = (
    #             "s3://test.dev.steeleye.co/aries/ingress/nonstreamed/evented/cloud9_voice_poll/2024/05/16/random_trace_id/20240723_S0vUym6AAr.json"  # noqa: E501
    #         )
    #         bucket_name = get_bucket(sample_aries_task_input.input_param.params["file_uri"])
    #         create_and_add_objects_to_s3_bucket(bucket_name=bucket_name)
    #
    #         aries_task_result = self._run_aries_task(  # type: ignore
    #             sample_aries_task_input=sample_aries_task_input,
    #             link_participants_scroll_result=link_participants_scroll_result,
    #             bucket_name=bucket_name,
    #             mocker=mocker,
    #         )
    #
    #     assert (
    #         "Skipping audio extraction for file: 'cDt-20230310-100940-simaujla1-7242272-1.opus' "  # noqa: E501
    #         "with `uniqueCallID`: '7169565cc2a646f4ac3958aea5a7325d' due to error: Offset cannot be negative: -3.0"  # noqa: E501
    #     ) in caplog.text
    #
    #     ndjon_s3_uri: str = (
    #         aries_task_result.output_param.params.get("Call").get("params").get("file_uri")
    #     )
    #
    #     waveform_s3_uri: str = (
    #         aries_task_result.output_param.params.get("Waveform").get("params").get("file_uri")
    #     )
    #
    #     local_file_path: str = run_download_file(
    #         file_url=ndjon_s3_uri,
    #     )
    #
    #     final_result_expected: pd.DataFrame = pd.read_json(
    #         EXPECTED_OUTPUT_SHOUTDOWN_NDJSON_PATH.as_posix(), lines=True
    #     )
    #
    #     final_result: pd.DataFrame = pd.read_json(local_file_path, lines=True)
    #
    #     # Sort identifiers columns
    #     sort_identifiers_columns(result_df=final_result, expected_result_df=final_result_expected)
    #
    #     # First and last rows will be ignored as its pd.NA,
    #     # `equal_nan` not used because of the `pd.NA` values
    #     # comparing with a tolerance of 20 bytes, as it may differ due to many reasons
    #     assert (
    #         np.allclose(
    #             final_result.loc[1:2, "voiceFile"].str["fileInfo"].str["contentLength"],  # type: ignore[index] # noqa: E501
    #             final_result_expected.loc[1:2, "voiceFile"].str["fileInfo"].str["contentLength"],  # type: ignore[index] # noqa: E501
    #             atol=30,
    #         )
    #         is True
    #     )
    #
    #     assert (
    #         np.allclose(
    #             final_result.loc[1:2, "voiceFile"].str["sizeInBytes"],  # type: ignore[index] # noqa: E501
    #             final_result_expected.loc[1:2, "voiceFile"].str["sizeInBytes"],  # type: ignore[index] # noqa: E501
    #             atol=30,
    #         )
    #         is True
    #     )
    #
    #     # Drop sizeInBytes and contentLength columns as they are compared above
    #     final_result["voiceFile"] = final_result["voiceFile"].apply(
    #         lambda x: TestCloud9VoiceTransform.drop_size_in_bytes_and_content_length_columns(x)
    #     )
    #
    #     final_result_expected["voiceFile"] = final_result_expected["voiceFile"].apply(
    #         lambda x: TestCloud9VoiceTransform.drop_size_in_bytes_and_content_length_columns(x)
    #     )
    #
    #     pd.testing.assert_frame_equal(
    #         left=final_result.sort_values(by="id")
    #         .drop(["sourceIndex"], axis=1)
    #         .reset_index(drop=True),
    #         right=final_result_expected.sort_values(by="id")
    #         .drop(["sourceIndex"], axis=1)
    #         .reset_index(drop=True),
    #         check_like=True,
    #     )
    #
    #     # Recipient does not have a link by participant in the second call
    #     # Sender ID's format is a bit wonky
    #     assert_record_is_schema_compliant(
    #         input_df=final_result,
    #         model=Call,
    #         accepted_validation_error_codes=[
    #             SteeleyeRecordErrorCodesEnum.SE_DV_521,
    #             SteeleyeRecordErrorCodesEnum.SE_DV_522,
    #             SteeleyeRecordErrorCodesEnum.SE_DV_547,
    #             SteeleyeRecordErrorCodesEnum.SE_DV_551,  # Expected we are dropping the sizeInBytes and contentLength # noqa: E501
    #         ],
    #     )
    #
    #     metrics = (
    #         aries_task_result.app_metric.metrics[StaticFields.DATA_INTEGRATION_METRICS_PREFIX][
    #             "cloud9_voice"
    #         ]["cloud9_voice_transform"]
    #         if aries_task_result.app_metric
    #         else {}
    #     )
    #
    #     assert check_file_exists(
    #         path=f"s3://{bucket_name}/aries/ingress/depository/attachments/cloud9_voice_poll/2024/05/16/random_trace_id/segments/cDt-20230310-100940-simaujla1-7242272-1_segment_0_35000.opus"  # noqa: E501
    #     )
    #
    #     assert check_file_exists(
    #         path=f"s3://{bucket_name}/aries/ingress/depository/attachments/cloud9_voice_poll/2024/05/16/random_trace_id/segments/cDt-20230310-100940-simaujla1-7242272-2_segment_0_9000.opus"  # noqa: E501
    #     )
    #
    #     assert metrics[GenericAppMetricsEnum.OUTPUT_COUNT] == 4
    #
    #     assert aries_task_result.output_param.params == {
    #         "Call": {
    #             "params": {
    #                 "file_uri": ndjon_s3_uri,
    #                 "es_action": EsActionEnum.INDEX.value,
    #                 "data_model": "se_elastic_schema.models.tenant.communication.call:Call",
    #             }
    #         },
    #         "Waveform": {
    #             "params": {
    #                 "file_uri": waveform_s3_uri,
    #             }
    #         },
    #     }

    @staticmethod
    def drop_size_in_bytes_and_content_length_columns(x):
        if pd.isna(x):
            return x

        x.pop("sizeInBytes")
        x["fileInfo"].pop("contentLength")
        return x

    @staticmethod
    @patch.object(link_participants, "get_repository_by_cluster_version")
    @patch.object(link_participants, "get_es_config")
    def _run_aries_task(
        mock_link_participants_get_es_config,
        mock_link_participants_elasticsearch_repository,
        link_participants_scroll_result: pd.DataFrame,
        sample_aries_task_input: AriesTaskInput,
        bucket_name: str,
        mocker,
    ):
        """Runs the flow after uploading the input files to mock S3 and mocking
        functions which read from Elasticsearch.

        :param mock_link_participants_get_es_config: Unittest patch object
        :param mock_link_participants_elasticsearch_repository: Unittest patch object
        :param link_participants_scroll_result: Mock LinkParticipants scroll result
        :return:
        """
        aries_task_input = sample_aries_task_input
        mocker.patch.object(
            target=CachedTenantWorkflowAPIClient,
            attribute="get",
            return_value=addict.Dict(
                {
                    "tenant": {
                        "lake_prefix": f"s3://{bucket_name}",
                    },
                    "workflow": {"streamed": False},
                },
            ),
        )
        # Mocks for link_participants elastic config
        mock_link_participants_get_es_config.return_value = ResourceConfig(
            host="localhost",
            port=9200,
            scheme="http",
            meta_prefix=MetaPrefix.AMPERSAND,
        )

        # Mocks link participants elastic repository
        es_obj = mock_link_participants_elasticsearch_repository.return_value
        es_obj.scroll.return_value = link_participants_scroll_result
        es_obj.MAX_TERMS_SIZE = 1024
        es_obj.meta.prefix = MetaPrefix.AMPERSAND
        es_obj.meta.key = "&key"
        es_obj.meta.id = "&id"

        # Run flow
        return cloud9_voice_transform_run(aries_task_input=aries_task_input)


def create_and_add_objects_to_s3_bucket(bucket_name: str):
    """Recreate the s3 bucket, and copy all the files from `LOCAL_BUCKET_PATH`
    to s3 mocked bucket.

    :param bucket_name: Bucket name of Mock S3 bucket
    :return: None
    """

    # Create bucket
    s3 = boto3.client("s3", region_name="us-east-1")
    s3.create_bucket(Bucket=bucket_name)
    local_bucket_path = LOCAL_BUCKET_PATH.joinpath(bucket_name)
    for file_ in local_bucket_path.rglob("*"):
        if file_.is_file():
            _path = file_.as_posix().replace(f"{local_bucket_path}/", "")

            with open(file_, "rb") as f:
                s3.put_object(Bucket=bucket_name, Key=_path, Body=f.read())
