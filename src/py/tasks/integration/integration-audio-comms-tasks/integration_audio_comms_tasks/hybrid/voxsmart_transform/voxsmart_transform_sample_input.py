from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput
from datetime import datetime


def sample_input() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        trace_id="test_voxsmart_1",
        name="voxsmart",
        stack="uat-shared-1",
        tenant="puneeth",
        start_timestamp=datetime.now(),
    )

    input_param = IOParamFieldSet(
        params=dict(
            file_uri="s3://puneeth.uat.steeleye.co/aries/ingress/nonstreamed/evented/voxsmart/aem-whatsapp-20250516-195044.zip",  # noqa E501
            dynamic_tasks=dict(
                call=dict(
                    name="comms_voice_sub_workflow",
                    task_reference_name="comms_voice_sub_workflow_ref",
                    type="SUB_WORKFLOW",
                ),
                waveform=dict(
                    name="elastic_ingestion",
                    task_reference_name="waveform_transform_ref",
                    type="SIMPLE",
                ),
                message=dict(
                    name="elastic_ingestion",
                    task_reference_name="elastic_ingestion_message_ref",
                    type="SUB_WORKFLOW",
                ),
            ),
        )
    )

    task = TaskFieldSet(name="voxsmart_transform", version="latest", success=False)

    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)
