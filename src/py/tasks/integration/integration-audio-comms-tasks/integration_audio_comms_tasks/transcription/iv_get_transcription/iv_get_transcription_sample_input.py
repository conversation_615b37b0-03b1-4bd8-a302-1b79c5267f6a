from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput
from datetime import datetime


def sample_input() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        trace_id="test_iv_get_1",
        name="iv_get_transcription",
        stack="dev-blue",
        tenant="ashwath",
        start_timestamp=datetime.now(),
    )
    input_param = IOParamFieldSet(
        params=dict(
            file_uri="s3://ashwath.dev.steeleye.co/aries/",
            dynamic_task=dict(
                name="iv_get_transcription_sub_workflow",
                task_reference_name="iv_get_transcription_sub_workflow_ref",
                type="SUB_WORKFLOW",
            ),
        )
    )
    task = TaskFieldSet(name="iv_get_transcription", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)
