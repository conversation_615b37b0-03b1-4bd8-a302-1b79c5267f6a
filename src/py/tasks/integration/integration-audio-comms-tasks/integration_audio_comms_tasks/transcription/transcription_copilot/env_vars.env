DATA_PLATFORM_CONFIG_API_URL=https://config-api.nonprod-eu-ie-1.steeleye.co
ELASTIC_API_KEY=akEyZUs1QUIyenJaWEo4MV9oX1Y6LXhwdU5lVlZSLW1rQWk4LVVBQnVSQQ==
ELASTIC_HOST=elasticsearch.uat-shared-steeleye.steeleye.co
ELASTIC_PORT=443
TASK_NAME=aries_transcription_copilot
TASK_WORKER_DOMAIN=uat-shared-steeleye
DEBUG=1
OMA_REST_PROXY_URL=https://kafka-rest.dev-oma.steeleye.co
OMA_ARIES_TOPIC=aries.oma.events
CONDUCTOR_API_URL=https://conductor.dev-enterprise.steeleye.co/api
STACK=uat-shared-steeleye
AWS_PROFILE=nonprod_infra
OPENAI_API_BASE=https://openai-internal-tenants.openai.azure.com
OPENAI_USE_GPT_4_O=True
OPENAI_API_KEY=********************************
OPENAI_API_MODEL=gpt-4o
OPENAI_API_TYPE=azure
OPENAI_API_VERSION=2024-06-01
BENCHMARK=false
#BENCHMARK=true