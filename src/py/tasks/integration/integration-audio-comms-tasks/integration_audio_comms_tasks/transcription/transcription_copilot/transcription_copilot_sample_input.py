from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput
from datetime import datetime


def sample_input() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        trace_id="test_transcription_copilot_1",
        name="xima_voice",
        stack="uat-shared-steeleye",
        tenant="iris",
        start_timestamp=datetime.now(),
    )
    input_param = IOParamFieldSet(
        params={
            "Transcript": {
                "params": {
                    "file_uri": "s3://ben.uat.steeleye.co/aries/ingest/cloud9_voice/2025/06/02/QooHbsbtS8_ZnvqRyxvVb/aries_deepgram_feed/e6bd9cd553e60e35c46bab215f4acccd4db3b304795849e5988906aecc4b991b___transcript___transformed.ndjson",
                    "es_action": "create",
                    "data_model": "se_elastic_schema.models.tenant.communication.transcript:Transcript"
                }
            },
            "Call": {
                "params": {
                    "file_uri": "s3://ben.uat.steeleye.co/aries/ingest/cloud9_voice/2025/06/02/QooHbsbtS8_ZnvqRyxvVb/aries_deepgram_feed/e6bd9cd553e60e35c46bab215f4acccd4db3b304795849e5988906aecc4b991b___call___transformed.ndjson",
                    "es_action": "update",
                    "data_model": "se_elastic_schema.models.tenant.communication.call:Call"
                }
            },
            "tenant_config_feature_flags": ["azure-processing"],
        }
    )
    task = TaskFieldSet(name="iv_transform_transcription", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)
