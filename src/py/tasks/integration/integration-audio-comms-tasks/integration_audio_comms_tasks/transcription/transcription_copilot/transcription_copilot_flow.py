# type: ignore
import logging
import os
import pandas as pd
from addict import Dict
from api_sdk.auth import tenant_from_realm
from aries_se_comms_tasks.transcription.transcript_copilot.copilot_call_frame_with_transcript_fields import (  # noqa E501
    run_copilot_call_frame_with_transcript_fields,
)
from aries_se_comms_tasks.transcription.transcript_copilot.transcript_copilot import (
    run_transcript_copilot,
)
from aries_se_comms_tasks.transcription.static import TranscriptModelFields  # Added missing import
from aries_se_comms_tasks.voice.static import CallColumns, SemanticModelFields
from aries_se_core_tasks.aries.utility_tasks.finish_flow import add_nested_params, finish_flow
from aries_se_core_tasks.aries.utility_tasks.get_tenant_bucket import get_tenant_bucket
from aries_se_core_tasks.aries.utility_tasks.unpack_aries_task_input import unpack_aries_task_input
from aries_se_core_tasks.datetime.get_seconds_from_time import Params as GetSecondsFromTimeParams
from aries_se_core_tasks.datetime.get_seconds_from_time import run_get_seconds_from_time
from aries_se_core_tasks.frame.filter_columns import Params as ParamsFilterColumns
from aries_se_core_tasks.frame.filter_columns import run_filter_columns
from aries_se_core_tasks.frame.frame_concatenator import Params as ParamsFrameConcatenator
from aries_se_core_tasks.frame.frame_concatenator import run_frame_concatenator
from aries_se_core_tasks.io.create_ndjson_path import create_ndjson_path
from aries_se_core_tasks.io.read.cloud.download_file import run_download_file
from aries_se_core_tasks.io.write.write_ndjson import run_write_ndjson
from aries_se_core_tasks.static import MetaModel
from aries_se_core_tasks.utilities.serializer import serializer
from aries_task_link.models import AriesTaskInput
from integration_audit.auditor import AuditorStaticFields, upsert_audit
from integration_audio_comms_tasks.transcription.transcription_copilot.input_schema import (
    TranscriptionCopilotAriesTaskInput,
)
from omegaconf import OmegaConf
from pathlib import Path
from se_core_tasks.frame.filter_columns import ActionEnum
from se_core_tasks.frame.frame_concatenator import OrientEnum
from se_data_lake.cloud_utils import (
    get_bucket,
    get_cloud_provider_from_file_uri,
    get_cloud_provider_prefix,
)
from se_elastic_schema.models.tenant.communication.call import Call
from se_elastic_schema.static.tenant_configuration import FeatureFlags
from se_enums.elastic_search import EsActionEnum
from se_io_utils.json_utils import ndjson_to_flat_dataframe

logger = logging.getLogger(__name__)


# Define temporary column names as constants to avoid issues
class TranscriptionTempColumns:
    RECORDING_SOURCE_KEY = "__temp_recording_source_key__"
    CALL_DURATION_IN_SECONDS = "__temp_duration_seconds__"


# Set OpenAI environment variables
# Note: Consider moving these to a config file or environment for security
os.environ["OPENAI_API_BASE"] = "https://openai-internal-sweden.openai.azure.com/"
os.environ["OPENAI_API_KEY"] = "********************************"
os.environ["OPENAI_API_VERSION"] = "2025-01-01-preview"
os.environ["OPENAI_API_TYPE"] = "azure"
os.environ["OPENAI_MAX_ASYNC_REQUESTS_IN_A_BATCH"] = "500"
os.environ["OPENAI_API_MODEL"] = "gpt-4o-mini"


@serializer
def deduplicate_columns(df, positional_keep="first"):
    return df.loc[:, ~df.columns.duplicated(keep=positional_keep)]


def filter_calls_and_transcripts_by_duration(
        call_frame: pd.DataFrame,
        transcript_frame: pd.DataFrame,
        cloud_provider_prefix: str,
        audit_path: str,
        streamed: bool,
        duration_threshold_seconds: int = 10
) -> tuple[pd.DataFrame, pd.DataFrame]:
    """
    Filter both call and transcript frames together based on call duration threshold.
    Uses the same mask for both frames to ensure perfect consistency.

    Args:
        call_frame: DataFrame containing call data
        transcript_frame: DataFrame containing transcript data
        cloud_provider_prefix: Cloud provider prefix for constructing recording source keys
        audit_path: Path for audit logging
        streamed: Boolean flag for audit streaming
        duration_threshold_seconds: Minimum duration threshold in seconds (default: 10)

    Returns:
        tuple: (filtered_call_frame, filtered_transcript_frame)
    """
    # Handle empty frames
    if call_frame.empty:
        logger.warning("Call frame is empty. No duration filtering needed.")
        return call_frame, transcript_frame

    if transcript_frame.empty:
        logger.warning("Transcript frame is empty. Returning original call frame.")
        return call_frame, transcript_frame

    # Check if call duration column exists
    if CallColumns.CALL_DURATION not in call_frame.columns:
        logger.warning(f"Column '{CallColumns.CALL_DURATION}' not found in call frame. Skipping duration filtering.")
        return call_frame, transcript_frame

    # Check if transcript recording source key exists
    # Based on your CSV, the column is named 'recordingSourceKey'
    recording_source_key_col = TranscriptModelFields.RECORDING_SOURCE_KEY
    if recording_source_key_col not in transcript_frame.columns:
        # Try alternative column name from your CSV
        if 'recordingSourceKey' in transcript_frame.columns:
            recording_source_key_col = 'recordingSourceKey'
        else:
            logger.warning(
                f"Column '{TranscriptModelFields.RECORDING_SOURCE_KEY}' or 'recordingSourceKey' not found in transcript frame. Skipping duration filtering.")
            return call_frame, transcript_frame

    # Check required call columns for linking
    required_call_columns = [
        CallColumns.VOICE_FILE_FILE_INFO_LOCATION_BUCKET,
        CallColumns.VOICE_FILE_FILE_INFO_LOCATION_KEY
    ]

    if not all(col in call_frame.columns for col in required_call_columns):
        logger.warning(f"Required call columns {required_call_columns} not found. Skipping duration filtering.")
        return call_frame, transcript_frame

    # Check if there are any non-null call durations
    duration_not_null_mask = call_frame[CallColumns.CALL_DURATION].notnull()
    if not duration_not_null_mask.any():
        logger.warning("All call durations are null. Skipping duration filtering.")
        return call_frame, transcript_frame

    try:
        # Step 1: Create recording source keys for calls to link with transcripts
        # IMPORTANT: Create a copy to avoid modifying the original frame
        call_frame_with_source_key = call_frame.copy()
        call_frame_with_source_key[TranscriptionTempColumns.RECORDING_SOURCE_KEY] = (
                cloud_provider_prefix
                + call_frame[CallColumns.VOICE_FILE_FILE_INFO_LOCATION_BUCKET]
                + "/"
                + call_frame[CallColumns.VOICE_FILE_FILE_INFO_LOCATION_KEY]
        )

        # Step 2: Convert call duration from HH:MM:SS format to seconds
        call_frame_with_seconds = run_get_seconds_from_time(
            source_frame=call_frame_with_source_key,
            params=GetSecondsFromTimeParams(
                source_time_attribute=CallColumns.CALL_DURATION,
                target_attribute=TranscriptionTempColumns.CALL_DURATION_IN_SECONDS,
                source_time_format="%H:%M:%S",
            ),
            skip_serializer=True,
        )

        # Step 3: Create duration filter mask
        duration_seconds_col = call_frame_with_seconds[TranscriptionTempColumns.CALL_DURATION_IN_SECONDS]

        # Convert to numeric to handle any string values
        duration_seconds_col = pd.to_numeric(duration_seconds_col, errors='coerce')

        duration_gte_threshold_mask = (
                duration_seconds_col.notnull() &
                (duration_seconds_col >= duration_threshold_seconds)
        )

        # Step 4: Get the recording source keys for calls that meet the duration threshold
        valid_recording_source_keys = call_frame_with_source_key.loc[
            duration_gte_threshold_mask, TranscriptionTempColumns.RECORDING_SOURCE_KEY
        ].tolist()

        # Step 5: Filter call frame using the duration mask
        filtered_call_frame = call_frame.loc[duration_gte_threshold_mask]

        # Step 6: Filter transcript frame using the same recording source keys
        transcript_mask = transcript_frame[recording_source_key_col].isin(valid_recording_source_keys)
        filtered_transcript_frame = transcript_frame.loc[transcript_mask]

        # Step 7: Audit skipped calls and transcripts
        short_calls_mask = (
                duration_seconds_col.notnull() &
                (duration_seconds_col < duration_threshold_seconds)
        )

        if short_calls_mask.any():
            short_calls_count = short_calls_mask.sum()
            skipped_transcripts_count = len(transcript_frame) - len(filtered_transcript_frame)

            logger.info(
                f"Filtering out {short_calls_count} call(s) and {skipped_transcripts_count} transcript(s) "
                f"with duration less than {duration_threshold_seconds} seconds."
            )

            # Get call IDs for auditing (use index if ID column not available)
            if CallColumns.ID in call_frame.columns:
                short_call_ids = call_frame.loc[short_calls_mask, CallColumns.ID].fillna("unknown").astype(str).tolist()
            else:
                short_call_ids = [f"call_{idx}" for idx in call_frame.loc[short_calls_mask].index]

            # Audit skipped calls
            upsert_audit(
                audit_path=audit_path,
                streamed=streamed,
                input_data={
                    call_id: {
                        AuditorStaticFields.SKIPPED: 1,
                        AuditorStaticFields.STATUS: [
                            f"Skipped call and associated transcript as duration is less than {duration_threshold_seconds} seconds."
                        ],
                    }
                    for call_id in short_call_ids
                },
                models=[Call],
            )

        # Step 8: Log results
        if filtered_call_frame.empty:
            logger.warning(
                f"All calls have duration less than {duration_threshold_seconds} seconds. "
                f"Returning empty DataFrames."
            )
        else:
            logger.info(
                f"Duration filtering complete: {len(filtered_call_frame)} calls and "
                f"{len(filtered_transcript_frame)} transcripts remaining."
            )

        return filtered_call_frame, filtered_transcript_frame

    except Exception as e:
        logger.error(f"Error during duration filtering: {e}", exc_info=True)
        logger.warning("Returning original frames due to filtering error.")
        return call_frame, transcript_frame


def transcription_copilot_flow(
        aries_task_input: AriesTaskInput,
        app_metrics_path: str,
        audit_path: str,
        result_path: str = "result.json",
        streamed: bool = False,
):
    # SETUP #

    # Parse and validate AriesTaskInput parameters
    transcription_copilot_flow_input = unpack_aries_task_input(
        aries_task_input=aries_task_input, model=TranscriptionCopilotAriesTaskInput
    )
    transcript_source_file_path = transcription_copilot_flow_input.Transcript["params"]["file_uri"]
    call_source_file_path = transcription_copilot_flow_input.Call["params"]["file_uri"]

    realm: str = get_bucket(file_uri=transcript_source_file_path)
    tenant: str = tenant_from_realm(realm=realm)

    cloud_provider = get_cloud_provider_from_file_uri(file_uri=call_source_file_path)
    cloud_provider_prefix = get_cloud_provider_prefix(value=cloud_provider)

    if (
            FeatureFlags.AZURE_PROCESSING.value
            not in transcription_copilot_flow_input.tenant_config_feature_flags
    ):
        logger.warning(f"Tenant:{tenant} not eligible for copilot, finishing flow")
        transcript_output = add_nested_params(
            file_uri=transcript_source_file_path,
            es_action=EsActionEnum.CREATE,
            data_model="se_elastic_schema.models.tenant.communication.transcript:Transcript",
        )
        call_output = add_nested_params(
            file_uri=call_source_file_path,
            es_action=EsActionEnum.UPDATE,
            data_model="se_elastic_schema.models.tenant.communication.call:Call",
        )

        finish_flow(
            result_path=result_path,
            result_data={
                MetaModel.TRANSCRIPT: transcript_output,
                MetaModel.CALL: call_output,
            },
        )
        return

    # Download remote files from cloud
    transcript_local_file_path = run_download_file(file_url=transcript_source_file_path)
    call_local_file_path = run_download_file(file_url=call_source_file_path)

    # Determine the cloud Bucket of the tenant
    tenant_bucket_with_cloud_prefix = get_tenant_bucket(
        task_input=Dict(file_uri=transcript_source_file_path),
        cloud_provider_prefix=cloud_provider_prefix,
    )

    # Load the file containing configs and initialize vault client
    config = OmegaConf.load(Path(__file__).parent.joinpath("config.yml"))

    # read source data for transcript and call into separate dataframes
    transcript_source_frame = ndjson_to_flat_dataframe(
        ndjson_file_path=transcript_local_file_path,
        skip_on_emtpy_frame=False,
    )
    call_source_frame = ndjson_to_flat_dataframe(
        ndjson_file_path=call_local_file_path,
        skip_on_emtpy_frame=False,
    )
    # END SETUP #

    # BUSINESS LOGIC #

    # Filter out calls and transcripts with duration less than 10 seconds together
    call_source_frame_filtered, transcript_source_frame_filtered = filter_calls_and_transcripts_by_duration(
        call_frame=call_source_frame,
        transcript_frame=transcript_source_frame,
        cloud_provider_prefix=cloud_provider_prefix,
        audit_path=audit_path,
        streamed=streamed,
        duration_threshold_seconds=10
    )

    # Initialize final frames with filtered data TODO
    transcript_final_frame = transcript_source_frame_filtered
    call_final_frame = call_source_frame_filtered

    # Only run copilot processing if we have valid data after filtering
    if not transcript_source_frame_filtered.empty and not call_source_frame_filtered.empty:
        logger.info("Running transcript copilot on filtered data")

        # Run Transcript Copilot task on filtered data
        transcript_copilot_result = run_transcript_copilot(
            source_frame=transcript_source_frame_filtered, config=config
        )

        # Concatenate copilot results with transcript frame
        transcript_frame_with_copilot = run_frame_concatenator(
            params=ParamsFrameConcatenator(
                orient=OrientEnum.horizontal.value,
            ),
            source_frame=transcript_source_frame_filtered,
            transcript_copilot_frame=transcript_copilot_result,
        )

        # Merge transcript fields into call frame
        call_final_frame = run_copilot_call_frame_with_transcript_fields(
            call_frame=call_source_frame_filtered,
            transcript_frame_with_copilot=transcript_frame_with_copilot,
            cloud_provider_prefix=cloud_provider_prefix,
        )

        # Handle potential duplicate columns
        call_final_frame = deduplicate_columns(df=call_final_frame)

        # Remove copilot-specific columns from transcript frame
        transcript_final_frame = run_filter_columns(
            source_frame=transcript_frame_with_copilot,
            params=ParamsFilterColumns(
                action=ActionEnum.drop,
                columns=[
                            CallColumns.ANALYTICS_COPILOT_ANALYTICS_RISKS,
                            CallColumns.ANALYTICS_COPILOT_ANALYTICS_METADATA,
                        ]
                        + SemanticModelFields.list(),
            ),
        )
    elif call_source_frame_filtered.empty and not call_source_frame.empty:
        logger.warning("All calls were filtered out due to duration threshold. Skipping copilot processing.")
    else:
        logger.info("Processing frames without copilot (empty source data)")

    # Create the path where the Transcript ndjson result is to be uploaded
    transcript_ndjson_path = create_ndjson_path(
        tenant_bucket=tenant_bucket_with_cloud_prefix,
        aries_task_input=aries_task_input,
        model=MetaModel.TRANSCRIPT,
    )

    # Write the transcript_final_frame into a ndjson file at the generated ndjson path
    run_write_ndjson(
        source_serializer_result=transcript_final_frame,
        output_filepath=transcript_ndjson_path,
        audit_output=True,
        app_metrics_path=app_metrics_path,
        audit_path=audit_path,
    )
    transcript_output = add_nested_params(
        file_uri=transcript_ndjson_path,
        es_action=EsActionEnum.INDEX,
        data_model="se_elastic_schema.models.tenant.communication.transcript:Transcript",
    )

    # Create the path where the Call ndjson result is to be uploaded
    call_ndjson_path = create_ndjson_path(
        tenant_bucket=tenant_bucket_with_cloud_prefix,
        aries_task_input=aries_task_input,
        model=MetaModel.CALL,
    )

    # Write the call_final_frame into a ndjson file at the generated ndjson path
    run_write_ndjson(
        source_serializer_result=call_final_frame,
        output_filepath=call_ndjson_path,
        audit_output=True,
        app_metrics_path=app_metrics_path,
        audit_path=audit_path,
    )
    call_output = add_nested_params(
        file_uri=call_ndjson_path,
        es_action=EsActionEnum.UPDATE,
        data_model="se_elastic_schema.models.tenant.communication.call:Call",
    )

    # finish the flow with both transcript and call results
    finish_flow(
        result_path=result_path,
        result_data={
            MetaModel.TRANSCRIPT: transcript_output,
            MetaModel.CALL: call_output,
        },
    )