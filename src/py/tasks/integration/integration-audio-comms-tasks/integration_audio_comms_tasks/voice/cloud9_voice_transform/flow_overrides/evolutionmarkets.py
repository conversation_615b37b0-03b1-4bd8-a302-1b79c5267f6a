import logging
import pandas as pd
from aries_se_comms_tasks.feeds.voice.cloud9_voice.static import (
    Cloud9VoiceSourceColumns,
    Cloud9VoiceTempColumns,
)
from aries_se_core_tasks.utilities.serializer import serializer
from integration_audio_comms_tasks.voice.cloud9_voice_transform.cloud9_voice_transform_flow import (
    Cloud9VoiceTransformFlow,
)
from se_data_lake.cloud_utils import get_file_uri
from se_enums.cloud import CloudProviderEnum

logger = logging.getLogger(__name__)


class EvolutionMarketsCloud9VoiceTransformFlow(Cloud9VoiceTransformFlow):
    """Overrides the Cloud9VoiceTransformFlow to use process_recording_attachment
    instead of extract_and_upload_calls for EvolutionMarkets tenant.
    """

    @serializer
    def process_recording_attachment(self,  df: pd.DataFrame, bucket: str, cloud_provider: CloudProviderEnum) -> pd.DataFrame:

        def create_recording_uri(row: pd.Series) -> str:
            key = row[Cloud9VoiceSourceColumns.STEELEYE_META_ATTACHMENT_PATH]
            if pd.isna(key):
                return pd.NA
            return get_file_uri(
                bucket=bucket,
                key=key,
                cloud_provider=cloud_provider,
            )

        df[Cloud9VoiceTempColumns.RECORDING_URI] = df.apply(create_recording_uri, axis=1)
        return df