# mypy: disable-error-code="attr-defined"
import logging
import os
import pandas as pd
from aries_config_cached_client.tenant_workflow import CachedTenantWorkflowAPIClient
from aries_se_comms_tasks.audio_waveform.trigger_waveform_generator import (
    Params as TriggerWaveFormGeneratorParams,
)
from aries_se_comms_tasks.audio_waveform.trigger_waveform_generator import (
    SkipIfFileNotUploaded,
    SkipIfMissingRequiredColumns,
    SkipIfSourceFrameEmpty,
    run_trigger_waveform_generator,
)
from aries_se_comms_tasks.feeds.voice.cloud9_voice.static import (
    CLOUD9_VOICE_FLOW_NAME,
    Cloud9VoiceSourceColumns,
    Cloud9VoiceTempColumns,
)
from aries_se_comms_tasks.generic.attachment.get_attachment_metadata import (
    Params as ParamsGetAttachmentMetadata,
)
from aries_se_comms_tasks.generic.attachment.get_attachment_metadata import (
    run_get_attachment_metadata,
)
from aries_se_comms_tasks.generic.participants.link_participants import (
    Params as LinkParticipantsParams,
)
from aries_se_comms_tasks.generic.participants.link_participants import run_link_participants
from aries_se_comms_tasks.voice.generic.has_attachment import run_has_attachment
from aries_se_comms_tasks.voice.static import CallColumns, Prefixes
from aries_se_core_tasks.aries.utility_tasks.finish_flow import add_nested_params, finish_flow
from aries_se_core_tasks.aries.utility_tasks.get_tenant_bucket import get_tenant_bucket
from aries_se_core_tasks.aries.utility_tasks.unpack_aries_task_input import unpack_aries_task_input
from aries_se_core_tasks.frame.frame_column_manipulator import (
    Params as ParamsFrameColumnManipulator,
)
from aries_se_core_tasks.frame.frame_column_manipulator import run_frame_column_manipulator
from aries_se_core_tasks.frame.frame_concatenator import Params as FrameConcatenatorParams
from aries_se_core_tasks.frame.frame_concatenator import Params as ParamsFrameConcatenator
from aries_se_core_tasks.frame.frame_concatenator import run_frame_concatenator
from aries_se_core_tasks.generic.generate_record_identifiers_for_df import (
    Params as GenerateRecordFileIdentifiersForDfParams,
)
from aries_se_core_tasks.generic.generate_record_identifiers_for_df import (
    run_generate_record_identifiers_for_df,
)
from aries_se_core_tasks.get_primary_transformations import run_get_primary_transformations
from aries_se_core_tasks.io.create_ndjson_path import create_ndjson_path
from aries_se_core_tasks.io.read.cloud.download_file import run_download_file
from aries_se_core_tasks.io.read.json_file_to_dataframe_converter import (
    Params as JsonFileToDataframeParams,
)
from aries_se_core_tasks.io.read.json_file_to_dataframe_converter import (
    run_json_file_to_dataframe_converter,
)
from aries_se_core_tasks.io.write.write_ndjson import run_write_ndjson
from aries_se_core_tasks.static import MetaModel
from aries_se_core_tasks.utilities.serializer import serializer
from aries_task_link.models import AriesTaskInput
from integration_audio_comms_tasks.voice.cloud9_voice_transform.extract_and_upload_calls import extract_and_upload_calls
from integration_audio_comms_tasks.voice.cloud9_voice_transform.input_schema import (
    Cloud9VoiceTransformAriesTaskInput,
)
from integration_audit.auditor import upsert_audit
from pathlib import Path
from se_core_tasks.frame.frame_column_manipulator import Action
from se_core_tasks.frame.frame_concatenator import OrientEnum
from se_data_lake.cloud_utils import (
    get_bucket,
    get_cloud_provider_from_file_uri,
    get_cloud_provider_prefix,
    get_file_uri,
)
from se_data_lake.lake_path import get_ingress_depository_lake_path_for_waveform
from se_elastic_schema.models import Call
from se_enums.cloud import CloudProviderEnum
from se_enums.elastic_search import EsActionEnum
from typing import Optional

logger = logging.getLogger(__name__)


DEFAULT_BATCH_SIZE: int = os.environ.get("DEFAULT_BATCH_SIZE", 1000)  # type: ignore[assignment]


class Cloud9VoiceTransformFlow:
    def process_recording_uris(
        self, df: pd.DataFrame, bucket: str, cloud_provider: CloudProviderEnum
    ) -> pd.DataFrame:
        """Process recording URIs for the dataframe. This method can be overridden
        by tenant-specific flows to use different URI processing strategies.

        By default, uses set_recording_uri_from_attachment_path which points directly
        to original audio files instead of extracting and uploading segments.

        :param df: DataFrame containing the source data
        :param bucket: The bucket name where the audio files are located
        :param cloud_provider: The cloud provider where the audio files are located
        :return: DataFrame with the updated RECORDING_URI column
        """
        return set_recording_uri_from_attachment_path(
            df=df,
            bucket=bucket,
            cloud_provider=cloud_provider,
        )

    def run_flow(
        self,
        aries_task_input: AriesTaskInput,
        app_metrics_path: Optional[str] = None,
        audit_path: Optional[str] = None,
        result_path: str = "result.json",
    ):
        """
        -------------------------------------------------------------
        ||              Cloud9VoiceTransform              ||
        -------------------------------------------------------------
        """
        # Parse and validate AriesTaskInput parameters
        cloud9_voice_transform_input = unpack_aries_task_input(
            aries_task_input=aries_task_input, model=Cloud9VoiceTransformAriesTaskInput
        )

        # Get tenant workflow tenant config from postgres
        cached_tenant_workflow_config = CachedTenantWorkflowAPIClient.get(
            stack_name=aries_task_input.workflow.stack,
            tenant_name=aries_task_input.workflow.tenant,
            workflow_name=aries_task_input.workflow.name,
        )

        # Get realm from input file path
        bucket: str = get_bucket(file_uri=cloud9_voice_transform_input.file_uri)
        tenant: str = aries_task_input.workflow.tenant
        source_file_url: str = cloud9_voice_transform_input.file_uri

        cloud_provider: CloudProviderEnum = get_cloud_provider_from_file_uri(
            file_uri=cached_tenant_workflow_config.tenant.lake_prefix
        )
        cloud_provider_prefix = get_cloud_provider_prefix(value=cloud_provider)

        streamed: bool = cached_tenant_workflow_config.workflow.streamed

        # Determine the Cloud Bucket of the tenant
        tenant_bucket_with_cloud_prefix = get_tenant_bucket(
            task_input=cloud9_voice_transform_input,
            cloud_provider_prefix=cloud_provider_prefix,
        )

        # Download remote file from cloud
        local_file_path: str = run_download_file(
            file_url=cloud9_voice_transform_input.file_uri,
        )
        # Will store the generated output paths
        ndjson_path: Optional[str] = None
        waveform_path: Optional[str] = None

        files_dataframe: pd.DataFrame = run_json_file_to_dataframe_converter(
            params=JsonFileToDataframeParams(
                normalize=True, columns=Cloud9VoiceSourceColumns.all()
            ),
            path=Path(local_file_path),
        )

        files_dataframe = self.filter_records_by_condition(
            df=files_dataframe,
            audit_path=audit_path,  # type:ignore[arg-type]
            streamed=streamed,
        )

        if files_dataframe.empty:
            logger.info("No valid calls to process. Exiting run_flow.")
            upsert_audit(
                audit_path=audit_path,
                streamed=streamed,
                workflow_status=[
                    "No valid calls to process, all call durations are less than 10 seconds."
                ],
            )

        else:
            files_dataframe_with_recording_uri = self.process_recording_attachment(
                df=files_dataframe,
                bucket=bucket,
                cloud_provider=cloud_provider,
            )

            data_exists: int = files_dataframe_with_recording_uri.shape[0]

            if data_exists > 0:
                primary_mappings_result = run_get_primary_transformations(
                    source_frame=files_dataframe_with_recording_uri,
                    flow=CLOUD9_VOICE_FLOW_NAME,
                    realm=bucket,
                    tenant=tenant,
                    source_file_url=source_file_url,
                    cloud_provider=cloud_provider,
                )

                # Get the Attachment head object details from the attachment url
                attachment_head_object_result = run_get_attachment_metadata(
                    source_frame=files_dataframe_with_recording_uri,
                    params=ParamsGetAttachmentMetadata(
                        attachment_column=Cloud9VoiceTempColumns.RECORDING_URI
                    ),
                    cloud_provider=cloud_provider,
                )

                # Add the 'voiceFile' prefix to the attachment columns
                attachment_with_voice_file_prefix_result = run_frame_column_manipulator(
                    source_frame=attachment_head_object_result,
                    params=ParamsFrameColumnManipulator(
                        action=Action.add, prefix=Prefixes.VOICE_FILE
                    ),
                )

                # Concatenate voice and attachment data
                concatenated_calls_result = run_frame_concatenator(
                    metadata_df=primary_mappings_result,
                    attachment_df=attachment_with_voice_file_prefix_result,
                    params=ParamsFrameConcatenator(
                        orient=OrientEnum.horizontal,
                    ),
                )

                # Populate HasAttachment and connected based on
                # whether an attachment was fetched or not
                has_attachment_result = run_has_attachment(source_frame=concatenated_calls_result)

                # Get the sourceKey, this is used for the audit keys
                call_frame_with_amp_id = run_generate_record_identifiers_for_df(
                    source_frame=concatenated_calls_result,
                    params=GenerateRecordFileIdentifiersForDfParams(
                        data_model=MetaModel.CALL, target_record_identifier_col="record_identifier"
                    ),
                    streamed=streamed,
                    cloud_provider=cloud_provider,
                )

                # Enrichment: Link participants based on the create participant identifiers
                participants_result = run_link_participants(
                    tenant=tenant,
                    source_frame=call_frame_with_amp_id,
                    params=LinkParticipantsParams(
                        target_participants_column=CallColumns.PARTICIPANTS
                    ),
                    streamed=streamed,
                    app_metrics_path=app_metrics_path,
                    audit_path=audit_path,
                    data_models=[Call],
                    record_identifier_column="record_identifier",
                )

                # Concatenate the participants data with the entire call and attachment data
                final_result = run_frame_concatenator(
                    call_and_attachment_df=has_attachment_result,
                    participants_df=participants_result,
                    params=FrameConcatenatorParams(orient=OrientEnum.horizontal),
                )
                final_records_length: int = final_result.shape[0]

                if final_records_length > 0:
                    # Create the appropriate path where the ndjson result is to be uploaded
                    ndjson_path = create_ndjson_path(
                        tenant_bucket=tenant_bucket_with_cloud_prefix,
                        aries_task_input=aries_task_input,
                        model=MetaModel.CALL,
                    )

                    # Write the transformed_df data frame into a ndjson file
                    # to the generated ndjson path
                    run_write_ndjson(
                        source_serializer_result=final_result,
                        output_filepath=ndjson_path,
                        audit_output=True,
                        app_metrics_path=app_metrics_path,
                        audit_path=audit_path,
                    )

                    waveform_prefix = get_ingress_depository_lake_path_for_waveform(
                        workflow_name=aries_task_input.workflow.name,
                        workflow_start_timestamp=aries_task_input.workflow.start_timestamp,
                        workflow_trace_id=aries_task_input.workflow.trace_id,
                        task_io_params=aries_task_input.input_param.params,
                    )

                    try:
                        waveform_path = run_trigger_waveform_generator(
                            source_frame=final_result,
                            realm=bucket,
                            waveform_path=waveform_prefix,
                            params=TriggerWaveFormGeneratorParams(
                                source_feed=CLOUD9_VOICE_FLOW_NAME,
                                cloud_provider_prefix=cloud_provider_prefix,
                            ),
                        )
                    except (
                        SkipIfMissingRequiredColumns,
                        SkipIfSourceFrameEmpty,
                        SkipIfFileNotUploaded,
                    ) as e:
                        # Reset waveform_path to None
                        logger.warning(
                            "Setting waveform path to None for cases where there was an error"
                            f"in trigger waveform. Exception: {e}"
                        )

        call_output = (
            add_nested_params(
                file_uri=ndjson_path,
                es_action=EsActionEnum.INDEX.value,
                data_model="se_elastic_schema.models.tenant.communication.call:Call",
            )
            if ndjson_path
            else None
        )

        waveform_output = (
            add_nested_params(
                file_uri=waveform_path,
            )
            if waveform_path
            else None
        )

        finish_flow(
            result_path=result_path,
            result_data={
                MetaModel.CALL: call_output,
                MetaModel.WAVEFORM: waveform_output,
            },
        )

    @serializer
    def filter_records_by_condition(
        self,
        df: pd.DataFrame,
        audit_path: str,
        streamed: bool,
    ) -> pd.DataFrame:
        return df

    @serializer
    def process_recording_attachment(
        self, df: pd.DataFrame, bucket: str, cloud_provider: CloudProviderEnum
    ) -> pd.DataFrame:
        """Process recording attachments for the dataframe. This method can be overridden
        by tenant-specific flows to use different attachment processing strategies.

        By default, uses set_recording_uri_from_attachment_path which points directly
        to original audio files instead of extracting and uploading segments.

        :param df: DataFrame containing the source data
        :param bucket: The bucket name where the audio files are located
        :param cloud_provider: The cloud provider where the audio files are located
        :return: DataFrame with the updated RECORDING_URI column
        """
        return set_recording_uri_from_attachment_path(
            df=df,
            bucket=bucket,
            cloud_provider=cloud_provider,
        )



def cloud9_voice_transform_flow(
    flow_override_class,
    aries_task_input: AriesTaskInput,
    app_metrics_path: str,
    audit_path: str,
    result_path: str = "result.json",
):
    flow_override_class().run_flow(
        aries_task_input=aries_task_input,
        app_metrics_path=app_metrics_path,
        audit_path=audit_path,
        result_path=result_path,
    )
