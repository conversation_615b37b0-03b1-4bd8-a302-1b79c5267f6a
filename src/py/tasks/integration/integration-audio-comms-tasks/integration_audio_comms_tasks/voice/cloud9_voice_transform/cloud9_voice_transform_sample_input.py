from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput
from datetime import datetime


def sample_input() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        trace_id="test_cloud9_voice_transform_local",
        name="cloud9_voice",
        stack="uat-shared-steeleye",
        tenant="ben",
        start_timestamp=datetime.now(),
    )
    input_param = IOParamFieldSet(
        params=dict(
            file_uri="s3://ben.uat.steeleye.co/aries/ingress/nonstreamed/evented/cloud9_voice_poll/2025/06/02/tPKPNrNQ3Jj1JQewwfGVx/20250602_8UNOuYYGX5.json",  # noqa E501
        )
    )
    task = TaskFieldSet(name="cloud9_voice_transform", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)
