# mypy: disable-error-code="attr-defined"
import logging
import pandas as pd
from aries_config_cached_client.tenant_workflow import CachedTenantWorkflowAPIClient
from aries_se_comms_tasks.audio_waveform.trigger_waveform_generator import (
    Params as TriggerWaveFormGeneratorParams,
)
from aries_se_comms_tasks.audio_waveform.trigger_waveform_generator import (
    SkipIfFileNotUploaded,
    SkipIfMissingRequiredColumns,
    SkipIfSourceFrameEmpty,
    run_trigger_waveform_generator,
)
from aries_se_comms_tasks.feeds.voice.speik_voice.static import (
    SPEIK_VOICE_FLOW_NAME,
    SpeikFileUrlColumns,
    SpeikSourceColumns,
)
from aries_se_comms_tasks.generic.attachment.get_attachment_metadata import (
    Params as GetAttachmentMetadataParams,
)
from aries_se_comms_tasks.generic.attachment.get_attachment_metadata import (
    run_get_attachment_metadata,
)
from aries_se_comms_tasks.generic.participants.link_participants import (
    Params as LinkParticipantsParams,
)
from aries_se_comms_tasks.generic.participants.link_participants import run_link_participants
from aries_se_comms_tasks.voice.generic.has_attachment import run_has_attachment
from aries_se_comms_tasks.voice.static import CallColumns, Prefixes
from aries_se_core_tasks.aries.dataframe_from_batch_ndjson import run_dataframe_from_batch_ndjson
from aries_se_core_tasks.aries.utility_tasks.finish_flow import add_nested_params, finish_flow
from aries_se_core_tasks.aries.utility_tasks.get_tenant_bucket import get_tenant_bucket
from aries_se_core_tasks.aries.utility_tasks.unpack_aries_task_input import unpack_aries_task_input
from aries_se_core_tasks.core.core_dataclasses import SerializerResult
from aries_se_core_tasks.frame.frame_column_manipulator import (
    Params as FrameColumnManipulatorParams,
)
from aries_se_core_tasks.frame.frame_column_manipulator import run_frame_column_manipulator
from aries_se_core_tasks.frame.frame_concatenator import Params as FrameConcatenatorParams
from aries_se_core_tasks.frame.frame_concatenator import run_frame_concatenator
from aries_se_core_tasks.frame.get_rows_by_condition import Params as GetRowsByConditionParams
from aries_se_core_tasks.frame.get_rows_by_condition import run_get_rows_by_condition
from aries_se_core_tasks.generic.generate_record_identifiers_for_df import (
    Params as GenerateRecordFileIdentifiersForDfParams,
)
from aries_se_core_tasks.generic.generate_record_identifiers_for_df import (
    run_generate_record_identifiers_for_df,
)
from aries_se_core_tasks.get_primary_transformations import run_get_primary_transformations
from aries_se_core_tasks.io.create_ndjson_path import create_ndjson_path
from aries_se_core_tasks.io.read.json_batch_csv_downloader import (
    Params as JsonBatchCsvDownloaderParams,
)
from aries_se_core_tasks.io.read.json_batch_csv_downloader import run_json_batch_csv_downloader
from aries_se_core_tasks.io.write.write_ndjson import run_write_ndjson
from aries_se_core_tasks.static import MetaModel
from aries_se_core_tasks.utilities.serializer import serializer
from aries_task_link.models import AriesTaskInput
from integration_audio_comms_tasks.voice.speik_voice_transform.input_schema import (
    SpeikVoiceTransformAriesTaskInput,
)
from se_core_tasks.frame.frame_column_manipulator import Action
from se_core_tasks.frame.frame_concatenator import OrientEnum
from se_data_lake.cloud_utils import (
    get_bucket,
    get_cloud_provider_from_file_uri,
    get_cloud_provider_prefix,
    get_file_uri,
)
from se_data_lake.lake_path import get_ingress_depository_lake_path_for_waveform
from se_elastic_schema.models import Call
from se_enums.cloud import CloudProviderEnum
from se_enums.elastic_search import EsActionEnum
from typing import Optional

logger = logging.getLogger(__name__)


def speik_voice_transform_flow(
    aries_task_input: AriesTaskInput,
    app_metrics_path: Optional[str] = None,
    audit_path: Optional[str] = None,
    result_path: str = "result.json",
):
    """
    -------------------------------------------------------------
    ||                   SpeikVoiceTransform                  ||
    \
    -------------------------------------------------------------
    """
    # Parse and validate AriesTaskInput parameters
    task_transform_input: SpeikVoiceTransformAriesTaskInput = unpack_aries_task_input(
        aries_task_input=aries_task_input, model=SpeikVoiceTransformAriesTaskInput
    )

    # Get realm from input file path
    realm: str = get_bucket(file_uri=task_transform_input.file_uri)
    tenant: str = aries_task_input.workflow.tenant

    # Get tenant workflow tenant config from postgres
    cached_tenant_workflow_config = CachedTenantWorkflowAPIClient.get(
        stack_name=aries_task_input.workflow.stack,
        tenant_name=aries_task_input.workflow.tenant,
        workflow_name=aries_task_input.workflow.name,
    )

    cloud_provider: CloudProviderEnum = get_cloud_provider_from_file_uri(
        file_uri=cached_tenant_workflow_config.tenant.lake_prefix
    )
    cloud_provider_prefix = get_cloud_provider_prefix(value=cloud_provider)

    streamed: bool = cached_tenant_workflow_config.workflow.streamed

    # Determine the Cloud Bucket of the tenant
    tenant_bucket_with_cloud_prefix = get_tenant_bucket(
        task_input=task_transform_input, cloud_provider_prefix=cloud_provider_prefix
    )

    ndjson_batch_result = run_dataframe_from_batch_ndjson(
        transform_input=task_transform_input,
        app_metrics_path=app_metrics_path,
        audit_path=audit_path,
        file_url_col=SpeikFileUrlColumns.METADATA_FILE_URL,
    )

    # Download all JSON files and use the keys as data frame columns
    json_parsed_result = run_json_batch_csv_downloader(
        params=JsonBatchCsvDownloaderParams(
            metadata_column=SpeikFileUrlColumns.METADATA_FILE_URL,
            dataframe_columns=SpeikSourceColumns.all(),
        ),
        source_frame=ndjson_batch_result,
        streamed=streamed,
        cloud_provider=cloud_provider,
        model=Call,
        should_audit_failures=True,
        app_metrics_path=app_metrics_path,
        audit_path=audit_path,
    )

    # Will store the generated output paths
    ndjson_path: Optional[str] = None
    waveform_path: Optional[str] = None

    if not json_parsed_result.empty:
        json_parsed_result = add_recording_urls(
            df=json_parsed_result, cloud_provider=cloud_provider, bucket=realm
        )

        primary_mappings_result = run_get_primary_transformations(
            source_frame=json_parsed_result,
            flow=SPEIK_VOICE_FLOW_NAME,
            realm=realm,
            tenant=tenant,
            source_file_uri=task_transform_input.file_uri,
        )

        # Get the Attachment head object details from the attachment url
        attachment_head_object_result = run_get_attachment_metadata(
            source_frame=json_parsed_result,
            params=GetAttachmentMetadataParams(
                attachment_column=SpeikFileUrlColumns.RECORDING_FILE_URL
            ),
            cloud_provider=cloud_provider,
        )

        # Add the 'voiceFile' prefix to the attachment columns
        attachment_with_voice_file_prefix_result = run_frame_column_manipulator(
            source_frame=attachment_head_object_result,
            params=FrameColumnManipulatorParams(action=Action.add, prefix=Prefixes.VOICE_FILE),
        )

        # Concatenate voice and attachment data
        concatenated_calls_result = run_frame_concatenator(
            metadata_df=primary_mappings_result,
            attachment_df=attachment_with_voice_file_prefix_result,
            params=FrameConcatenatorParams(orient=OrientEnum.horizontal),
        )

        calls_without_duplicates_result = run_get_rows_by_condition(
            params=GetRowsByConditionParams(
                query=f"`{CallColumns.METADATA_SOURCE_CLIENT}`.notnull()", skip_on_empty=True
            ),
            source_frame=concatenated_calls_result,
        )

        # Populate HasAttachment and connected based on whether an attachment was fetched or not
        has_attachment_result = run_has_attachment(source_frame=calls_without_duplicates_result)

        # Get the sourceKey, this is used for the audit keys
        call_frame_with_amp_id = run_generate_record_identifiers_for_df(
            source_frame=concatenated_calls_result,
            params=GenerateRecordFileIdentifiersForDfParams(
                data_model=MetaModel.CALL, target_record_identifier_col="record_identifier"
            ),
            streamed=streamed,
            cloud_provider=cloud_provider,
        )

        # Enrichment: Link participants based on the create participant identifiers
        participants_result = run_link_participants(
            tenant=tenant,
            source_frame=call_frame_with_amp_id,
            params=LinkParticipantsParams(target_participants_column=CallColumns.PARTICIPANTS),
            streamed=streamed,
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
            data_models=[Call],
            record_identifier_column="record_identifier",
        )

        # Concatenate the participants' data with the entire call and attachment data
        final_result: SerializerResult = run_frame_concatenator(
            call_and_attachment_df=has_attachment_result,
            participants_df=participants_result,
            params=FrameConcatenatorParams(orient=OrientEnum.horizontal),
        )

        final_records_length: int = final_result.shape[0]

        if final_records_length > 0:
            # Create the appropriate path where the ndjson result is to be uploaded
            ndjson_path = create_ndjson_path(
                tenant_bucket=tenant_bucket_with_cloud_prefix,
                aries_task_input=aries_task_input,
                model=MetaModel.CALL,
            )

            # Write the transformed_df data frame into a ndjson file to the generated ndjson path
            run_write_ndjson(
                source_serializer_result=final_result,
                output_filepath=ndjson_path,
                audit_output=True,
                app_metrics_path=app_metrics_path,
                audit_path=audit_path,
            )

            waveform_prefix = get_ingress_depository_lake_path_for_waveform(
                workflow_name=aries_task_input.workflow.name,
                workflow_start_timestamp=aries_task_input.workflow.start_timestamp,
                workflow_trace_id=aries_task_input.workflow.trace_id,
                task_io_params=aries_task_input.input_param.params,
            )

            try:
                waveform_path = run_trigger_waveform_generator(
                    source_frame=final_result,
                    realm=realm,
                    waveform_path=waveform_prefix,
                    params=TriggerWaveFormGeneratorParams(
                        source_feed=SPEIK_VOICE_FLOW_NAME,
                        cloud_provider_prefix=cloud_provider_prefix,
                    ),
                )
            except (
                SkipIfMissingRequiredColumns,
                SkipIfSourceFrameEmpty,
                SkipIfFileNotUploaded,
            ) as e:
                # Reset waveform_path to None
                logger.warning(
                    "Setting waveform path to None for cases where there was an error"
                    f"in trigger waveform. Exception: {e}"
                )

    call_output = (
        add_nested_params(
            file_uri=ndjson_path,
            es_action=EsActionEnum.INDEX.value,
            data_model=Call.get_reference().get_qualified_reference(),
        )
        if ndjson_path
        else None
    )

    waveform_output = (
        add_nested_params(
            file_uri=waveform_path,
        )
        if waveform_path
        else None
    )

    finish_flow(
        result_path=result_path,
        result_data={
            MetaModel.CALL: call_output,
            MetaModel.WAVEFORM: waveform_output,
        },
    )


@serializer
def add_recording_urls(df: pd.DataFrame, cloud_provider: CloudProviderEnum, bucket: str):
    """Add RECORDING_FILE_URL based on STEELEYE_META_ATTACHMENT_PATH.

    :param df: Input DataFrame
    :param cloud_provider: Cloud provider
    :param bucket: Bucket name
    :return: DataFrame with RECORDING_FILE_URL column added
    """
    if SpeikSourceColumns.STEELEYE_META_ATTACHMENT_PATH in df.columns:
        df[SpeikFileUrlColumns.RECORDING_FILE_URL] = df[
            SpeikSourceColumns.STEELEYE_META_ATTACHMENT_PATH
        ].apply(
            lambda path: get_file_uri(cloud_provider=cloud_provider, bucket=bucket, key=path)
            if pd.notna(path)
            else None
        )

    return df
