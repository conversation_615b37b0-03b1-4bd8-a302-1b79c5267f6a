from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput
from datetime import datetime


def sample_input() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        name="ms_teams_email_ing_poll",
        stack="dev-shared-2",
        tenant="irises8",
        start_timestamp=datetime.now(),
    )
    input_param = IOParamFieldSet(
        params=dict(
            microsoft_tenant_id=None,
            from_date="2025-01-01",
            to_date="2025-01-31",
            max_workers_poll="10",
            custom_lake_path="",
            should_event=True,
            max_batch_size=100,
            process_mime_content=True,
        )
    )
    task = TaskFieldSet(name="test", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)
