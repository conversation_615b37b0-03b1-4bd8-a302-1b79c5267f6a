# MSTeamsSubscriptionManagement

The MSTeamsSubscriptionManagement is a flow that manages subscriptions for Microsoft Teams users and channels.

- It gets all the subscriptions for user and teams/channels for a specific `tenant` and `microsoft_tenant_id` via **MSTeamsAPI**. The last one can not be **None**.
    - If there is no subscription or team/channel for that tenant, it will raise an *Error*
- For each configuration it will get the user details using **UserAPI** class and it will also get the team/channel details using **GroupAPI** class.
- As the pagination was added to this poller, for each page it will check if any existing subscriptions are no longer present in the graph subscriptions retrieved from the **MS Graph APIs** and it will delete them from the table using the MSTeamsAPI (data-platform-config) API client.

### Sample Event

```
workflow = WorkflowFieldSet(
    name="ms_teams_subscriptions_sync",
    stack="dev-blue",
    tenant="pinafore",
    start_timestamp=datetime.now(),
)
input_param = IOParamFieldSet()
task = TaskFieldSet(name="test", version="latest", success=False)
return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)
```

### Sample Event with microsoft_tenant_id
```
workflow = WorkflowFieldSet(
    name="ms_teams_subscriptions_sync",
    stack="dev-blue",
    tenant="pinafore",
    start_timestamp=datetime.now(),
)
input_param = IOParamFieldSet(
    params=dict(
        microsoft_tenant_id = "test"
    )
)
task = TaskFieldSet(name="test", version="latest", success=False)
return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)
```
