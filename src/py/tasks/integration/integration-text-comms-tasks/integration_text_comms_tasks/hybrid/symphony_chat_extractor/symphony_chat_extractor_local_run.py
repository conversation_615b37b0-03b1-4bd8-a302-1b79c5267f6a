import logging
from benchmark_mode import benchmark
from integration_text_comms_tasks.hybrid.symphony_chat_extractor.symphony_chat_extractor_sample_input import (  # noqa E501
    sample_input,
)
from integration_text_comms_tasks.integration_text_comms_tasks_task import (
    integration_text_comms_tasks_run,
)

logger = logging.getLogger("symhony_chat_extractor")


@benchmark
def main():
    logger.info("Starting execution...")
    output = integration_text_comms_tasks_run(aries_task_input=sample_input())
    logger.info(f"Finished executing with output {output}")


if __name__ == "__main__":
    main()
