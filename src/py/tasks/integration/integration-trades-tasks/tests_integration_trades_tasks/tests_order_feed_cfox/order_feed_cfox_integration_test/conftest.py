import datetime
import os
import pandas as pd
import pytest
from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput
from integration_wrapper.static import IntegrationAriesTaskVariables

# This needs to be here since the conftest is loaded first
# than any import on the test files
os.environ[IntegrationAriesTaskVariables.DATA_PLATFORM_CONFIG_API_URL] = (
    "https://test-enterprise.steeleye.co"
)
os.environ["COGNITO_CLIENT_ID"] = "some_id"
os.environ["COGNITO_CLIENT_SECRET"] = "some_secret"
os.environ["COGNITO_AUTH_URL"] = "some_auth_url"
os.environ["MASTER_DATA_HOST"] = "some_host"
os.environ["MASTER_API_DATA_HOST"] = "some_host"
os.environ["SRP_THROUGH_MASTER_DATA"] = "True"


@pytest.fixture()
def aries_task_input_aws() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        trace_id="test_order_feed_cfox",
        name="order_feed_cfox",
        stack="dev-shared-2",
        tenant="test",
        start_timestamp=datetime.datetime.utcnow(),
    )
    input_param = IOParamFieldSet(
        params=dict(
            file_uri="s3://test.dev.steeleye.co/order_feed_cfox_test_file.csv",
            skiprows=11,
            nrows=10,
        )
    )
    task = TaskFieldSet(name="order_feed_cfox", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)


@pytest.fixture()
def mic_lookup_file_df() -> pd.DataFrame:
    return pd.DataFrame(
        [
            {"Exchange Name": "ENX-NL", "MIC Code": "XAMS"},
            {"Exchange Name": "CBOE", "MIC Code": "CBOE"},
            {"Exchange Name": "ADEX", "MIC Code": "ADEX"},
            {"Exchange Name": "BM&F", "MIC Code": "BVMF"},
            {"Exchange Name": "CFFEX", "MIC Code": "XSGE"},
            {"Exchange Name": "CME", "MIC Code": "XCME"},
            {"Exchange Name": "COMEX", "MIC Code": "XCEC"},
            {"Exchange Name": "DGCX", "MIC Code": "DGCX"},
            {"Exchange Name": "OM", "MIC Code": "XSTO"},
            {"Exchange Name": "DME", "MIC Code": "DMEF"},
            {"Exchange Name": "ENX-FR", "MIC Code": "XPAR"},
            {"Exchange Name": "EUREX", "MIC Code": "XEUR"},
            {"Exchange Name": "GREEN", "MIC Code": "GREX"},
            {"Exchange Name": "HKEX", "MIC Code": "XHKG"},
            {"Exchange Name": "HKMEX", "MIC Code": "HKME"},
            {"Exchange Name": "LIFFE", "MIC Code": "IFLL"},
            {"Exchange Name": "NYBOT", "MIC Code": "IFUS"},
            {"Exchange Name": "IPE", "MIC Code": "IFEU"},
            {"Exchange Name": "IFAD", "MIC Code": "IFAD"},
            {"Exchange Name": "WCE", "MIC Code": "WCE"},
            {"Exchange Name": "NSEI", "MIC Code": "XNSE"},
            {"Exchange Name": "SAFEX", "MIC Code": "SAFX"},
            {"Exchange Name": "KOFEX", "MIC Code": "XKFE"},
            {"Exchange Name": "MCX-SX", "MIC Code": "MCXS"},
            {"Exchange Name": "ENX-PT", "MIC Code": "XLIS"},
            {"Exchange Name": "IDEM", "MIC Code": "XDMI"},
            {"Exchange Name": "MICEX", "MIC Code": "MISX"},
            {"Exchange Name": "ME", "MIC Code": "XMOS"},
            {"Exchange Name": "MEFF", "MIC Code": "XMRV"},
            {"Exchange Name": "NORDPOOL", "MIC Code": "NORD"},
            {"Exchange Name": "ICE-US", "MIC Code": "IFUS"},
            {"Exchange Name": "NYMEX", "MIC Code": "XNYM"},
            {"Exchange Name": "OSE", "MIC Code": "XOSE"},
            {"Exchange Name": "ROFEX", "MIC Code": "ROFX"},
            {"Exchange Name": "RTS", "MIC Code": "XRUS"},
            {"Exchange Name": "SGX-DT", "MIC Code": "XSES"},
            {"Exchange Name": "SMX", "MIC Code": "SMEX"},
            {"Exchange Name": "TFEX", "MIC Code": "TFEX"},
            {"Exchange Name": "TIFFE", "MIC Code": "XTKS"},
            {"Exchange Name": "TAIFEX", "MIC Code": "XTWT"},
            {"Exchange Name": "AMEX", "MIC Code": "XASE"},
            {"Exchange Name": "ASX", "MIC Code": "XASX"},
            {"Exchange Name": "BSE", "MIC Code": "XBOM"},
            {"Exchange Name": "BOX", "MIC Code": "BOXE"},
            {"Exchange Name": "ENX-BE", "MIC Code": "XBRU"},
            {"Exchange Name": "CFE", "MIC Code": "CFE"},
            {"Exchange Name": "CBOT", "MIC Code": "XCBT"},
            {"Exchange Name": "DCE", "MIC Code": "XDCE"},
            {"Exchange Name": "EEX", "MIC Code": "XEEE"},
            {"Exchange Name": "ELX", "MIC Code": "ELX"},
            {"Exchange Name": "MEXDER", "MIC Code": "XMEX"},
            {"Exchange Name": "HKEX", "MIC Code": "XHKG"},
            {"Exchange Name": "MCX", "MIC Code": "MCXS"},
            {"Exchange Name": "INE", "MIC Code": "XINE"},
            {"Exchange Name": "TURKDEX", "MIC Code": "XIST"},
            {"Exchange Name": "KANEX", "MIC Code": "XKEX"},
            {"Exchange Name": "MDEX", "MIC Code": "XMDX"},
            {"Exchange Name": "LME", "MIC Code": "XLME"},
            {"Exchange Name": "LSECURVE", "MIC Code": "LSEV"},
            {"Exchange Name": "MGE", "MIC Code": "MGE"},
            {"Exchange Name": "NCDEX", "MIC Code": "NCDE"},
            {"Exchange Name": "NYLIFFE", "MIC Code": "NLIF"},
            {"Exchange Name": "NLX", "MIC Code": "NLON"},
            {"Exchange Name": "ONEC", "MIC Code": "ONEC"},
            {"Exchange Name": "OB", "MIC Code": "XNAS"},
            {"Exchange Name": "PHLX", "MIC Code": "XPHL"},
            {"Exchange Name": "PWX", "MIC Code": "PWAX"},
            {"Exchange Name": "PSE", "MIC Code": "XPSE"},
            {"Exchange Name": "PXE", "MIC Code": "PXEE"},
            {"Exchange Name": "SICOM", "MIC Code": "SICM"},
            {"Exchange Name": "SFE", "MIC Code": "XSFE"},
            {"Exchange Name": "SHFE", "MIC Code": "XSGE"},
            {"Exchange Name": "SPSE", "MIC Code": "XPSE"},
            {"Exchange Name": "TASE", "MIC Code": "XTAE"},
            {"Exchange Name": "TSE", "MIC Code": "XTSE"},
            {"Exchange Name": "TOCOM", "MIC Code": "XTOM"},
            {"Exchange Name": "USE", "MIC Code": "XUSD"},
            {"Exchange Name": "WSE", "MIC Code": "XWAR"},
            {"Exchange Name": "ZCE", "MIC Code": "XZCE"},
            {"Exchange Name": "YIELDX", "MIC Code": "YLDX"},
            {"Exchange Name": "OSE", "MIC Code": "XOSE"},
        ]
    )
