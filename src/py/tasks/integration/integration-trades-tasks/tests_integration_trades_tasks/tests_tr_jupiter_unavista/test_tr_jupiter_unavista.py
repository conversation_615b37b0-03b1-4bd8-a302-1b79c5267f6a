# mypy: disable-error-code="union-attr, misc, attr-defined, no-any-return"
import boto3
import fsspec
import json
import pandas as pd
from addict import addict
from aries_config_cached_client.tenant_workflow import CachedTenantWorkflowAPIClient
from aries_se_core_tasks.io.read.cloud.download_file import run_download_file
from aries_task_link.models import AriesTaskInput, AriesTaskResult
from freezegun import freeze_time
from integration_test_utils.aws_helpers.patch_response import (
    mock_aiobotocore_convert_to_response_dict,
)
from integration_trades_tasks.tr_jupiter_unavista.tr_jupiter_unavista_task import (
    tr_jupiter_unavista_run,
)
from moto import mock_aws
from pathlib import Path
from se_io_utils.json_utils import write_named_temporary_json
from se_io_utils.tempfile_utils import tmp_directory

CURRENT_PATH = Path(__file__).parent
DATA_PATH = CURRENT_PATH.joinpath("data")
BUCKET_NAME = "test.dev.steeleye.co"
LOCAL_BUCKET_PATH = DATA_PATH.joinpath("buckets", BUCKET_NAME)

EXPECTED_RESULTS_PATH = DATA_PATH.joinpath("expected_results")

TEMP_DIR: Path = tmp_directory()
AUDIT_PATH: Path = TEMP_DIR.joinpath("audit.json")

mock_aiobotocore_convert_to_response_dict()


class TestTrJupiterUnavista:
    @staticmethod
    def teardown_method():
        # remove files after each test
        if AUDIT_PATH.exists():
            AUDIT_PATH.unlink()

    @mock_aws
    def test_end_to_end(
        self,
        mocker,
        sample_aries_task_input: AriesTaskInput,
    ):
        aries_task_result = self._run_aries_task(
            mocker=mocker, aries_task_input=sample_aries_task_input
        )

        local_file_path: str = run_download_file(
            file_url=aries_task_result.output_param.params["io_params_list"][0]["io_param"][
                "params"
            ]["file_uri"],
        )
        result = pd.read_csv(local_file_path)
        expected_result = pd.read_csv(
            EXPECTED_RESULTS_PATH.joinpath("raw_input_jupiter_unavista_batch_0.csv")
        )
        pd.testing.assert_frame_equal(left=result, right=expected_result)

        with open(AUDIT_PATH, "r") as file:
            real_audit = json.load(file)

        with open(EXPECTED_RESULTS_PATH.joinpath("expected_audit.json"), "r") as file:
            expected_audit = json.load(file)
        assert expected_audit == real_audit

    @staticmethod
    @mock_aws
    @freeze_time(time_to_freeze="2024-06-25 08:00:00.000000+00:00")
    def _run_aries_task(mocker, aries_task_input: AriesTaskInput) -> AriesTaskResult:
        # Create mock S3 bucket and add objects to it
        create_and_add_objects_to_s3_bucket(bucket_name=BUCKET_NAME)

        # replace write_named_temporary_json side effect
        write_named_temporary_json_mock = mocker.patch(
            "integration_wrapper.integration_aries_task.write_named_temporary_json"
        )
        write_named_temporary_json_mock.side_effect = write_named_temporary_json_side_effect
        mocker.patch("integration_audit.auditor.write_json")
        mocker.patch.object(
            target=CachedTenantWorkflowAPIClient,
            attribute="get",
            return_value=addict.Dict(
                {
                    "tenant": {
                        "lake_prefix": "s3://test.dev.steeleye.co",
                    },
                    "workflow": {"streamed": False},
                    "max_batch_size": 10000,
                },
            ),
        )

        # Run flow
        return tr_jupiter_unavista_run(aries_task_input=aries_task_input)  # type: ignore


def create_and_add_objects_to_s3_bucket(bucket_name: str):
    """Creates a mock s3 bucket and copies the input file(s) to it.

    :param bucket_name: Bucket name of Mock S3 bucket
    :return: None
    """

    # Create bucket
    s3 = boto3.client("s3", region_name="us-east-1")
    s3.create_bucket(Bucket=bucket_name)

    for file_ in LOCAL_BUCKET_PATH.rglob("*"):
        if file_.is_file():
            _path = file_.as_posix().replace(f"{LOCAL_BUCKET_PATH}/", "")

            with open(file_, "rb") as f:
                s3.put_object(Bucket=bucket_name, Key=_path, Body=f.read())


def write_named_temporary_json_side_effect(output_filename: str, **kwargs) -> str:
    if output_filename == "audit.json":
        with fsspec.open(AUDIT_PATH.as_posix(), "w") as file:
            json.dump({}, file)

        return AUDIT_PATH.as_posix()

    return write_named_temporary_json(output_filename=output_filename, **kwargs)
