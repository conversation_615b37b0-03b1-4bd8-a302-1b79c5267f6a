# type: ignore
import boto3
import pytest
from addict import addict
from aries_config_cached_client.mifir_scheduler_config import CachedMifirSchedulerConfigAPIClient
from aries_se_core_tasks.core.generic_app_metrics_enum import GenericAppMetricsEnum
from aries_task_link.models import AriesTaskInput
from freezegun import freeze_time
from integration_trades_tasks.arm_no_response_email.arm_no_response_email_task import (
    tr_arm_no_response_email_run,
)
from integration_wrapper.static import StaticFields
from mock.mock import MagicMock
from moto import mock_aws
from moto.core import DEFAULT_ACCOUNT_ID
from moto.ses import ses_backends
from pathlib import Path
from se_trades_tasks.tr.workflow.notifications.arm_no_response_email import (
    FailIfEmailCouldNotBeSent,
)
from typing import Any, Dict

PARENT_PATH = Path(__file__).parent.joinpath("data")
EXPECTED_RAW_DATA_PATH = PARENT_PATH.joinpath("raw_email_data")


class MockEs:
    meta = addict.Dict(dict(expiry="&expiry", timestamp="&timestamp"))

    @property
    def client(self):
        return MagicMock()


class TestArmNoResponseEmail:
    """Test suite for ARM No Response Email."""

    @mock_aws
    @freeze_time(time_to_freeze="2023-12-26 06:59:38.911459+00:00")
    def test_it_can_run_end_to_end(
        self,
        mocker,
        sample_aries_task_input: AriesTaskInput,
        tr_arm_no_response_config_data: Dict[str, Any],
        transaction_report_data: Dict[str, Any],
    ):
        mocker.patch(
            "aries_se_core_tasks.tr.arm_no_response_email_task.get_es_config",
            return_value=MagicMock(),
        )
        mocker.patch(
            "aries_se_core_tasks.tr.arm_no_response_email_task.get_repository_by_cluster_version",
            return_value=MockEs(),
        )
        mocker.patch(
            "se_trades_tasks.tr.workflow.notifications.pre_submission_email.create_tr_audits",
            return_value=MagicMock(),
        )
        mocker.patch(
            "se_trades_tasks.tr.workflow.notifications.arm_no_response_email.ArmNoResponseEmail.es_scroll",
            return_value=transaction_report_data,
        )
        mock_cached_tenant_workflow_client = mocker.patch.object(
            CachedMifirSchedulerConfigAPIClient, "get_config"
        )
        mock_cached_tenant_workflow_client.return_value = tr_arm_no_response_config_data

        region = "eu-west-1"
        ses_client = boto3.client("ses", region_name=region)
        ses_client.verify_email_identity(EmailAddress="<EMAIL>")

        mock_boto_session = mocker.patch(
            "se_trades_tasks.tr.workflow.notifications.email_utils.boto3.session.Session",
        )
        mock_boto_session.return_value.region_name = region

        aries_task_result = tr_arm_no_response_email_run(aries_task_input=sample_aries_task_input)

        with open(EXPECTED_RAW_DATA_PATH, "r") as fp:
            raw_email_data = fp.read()

        ses_backend = ses_backends[DEFAULT_ACCOUNT_ID][region]
        messages = ses_backend.sent_messages
        assert messages[0].source == "<EMAIL>"
        assert messages[0].destinations == ["<EMAIL>"]
        assert messages[0].raw_data == raw_email_data

        metrics = (
            aries_task_result.app_metric.metrics[StaticFields.DATA_INTEGRATION_METRICS_PREFIX][
                "tr_arm_no_response_email"
            ]["tr_arm_no_response_email"]
            if aries_task_result.app_metric
            else {}
        )

        assert metrics[GenericAppMetricsEnum.OUTPUT_COUNT] == 1

    @mock_aws
    @freeze_time(time_to_freeze="2023-12-25 06:59:38.911459+00:00")
    def test_error_metrics_emitted_when_there_is_an_error(
        self,
        mocker,
        sample_aries_task_input: AriesTaskInput,
        tr_arm_no_response_config_data: Dict[str, Any],
    ):
        mocker.patch(
            "aries_se_core_tasks.tr.arm_no_response_email_task.get_es_config",
            return_value=MagicMock(),
        )
        mocker.patch(
            "aries_se_core_tasks.tr.arm_no_response_email_task.get_repository_by_cluster_version",
            return_value=MockEs(),
        )
        mocker.patch(
            "aries_se_core_tasks.tr.arm_no_response_email_task.run_task_from_lib",
            side_effect=FailIfEmailCouldNotBeSent(),
        )
        mock_cached_tenant_workflow_client = mocker.patch.object(
            CachedMifirSchedulerConfigAPIClient, "get_config"
        )
        mock_cached_tenant_workflow_client.return_value = tr_arm_no_response_config_data

        # Testing if error was raised successfully by the core workflow task
        with pytest.raises(Exception):
            tr_arm_no_response_email_run(aries_task_input=sample_aries_task_input)
