import logging
from benchmark_mode import benchmark
from integration_trades_tasks.integration_trades_tasks_task import integration_trades_tasks_run
from integration_trades_tasks.order_blotter_to_restricted_list.order_blotter_to_restricted_list_sample_input import (  # noqa E501
    sample_input,
)
from integration_trades_tasks.order_blotter_to_restricted_list.order_blotter_to_restricted_list_task import (  # noqa E501
    order_blotter_to_restricted_list_run,
)

logger = logging.getLogger("order_blotter_to_restricted_list")


@benchmark
def main():
    logger.info("Starting execution...")
    output = integration_trades_tasks_run(aries_task_input=sample_input())
    logger.info(f"Finished executing with output {output}")


if __name__ == "__main__":
    main()
