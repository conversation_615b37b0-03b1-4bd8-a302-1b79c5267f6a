# mypy: disable-error-code="attr-defined, operator"
import logging
import pandas as pd
from addict import addict
from aries_config_cached_client.tenant_workflow import CachedTenantWorkflowAPIClient
from aries_se_core_tasks.aries.utility_tasks.finish_flow import finish_flow
from aries_se_core_tasks.aries.utility_tasks.get_tenant_bucket import get_tenant_bucket
from aries_se_core_tasks.aries.utility_tasks.unpack_aries_task_input import unpack_aries_task_input
from aries_se_core_tasks.core.core_dataclasses import CloudAction, CloudFile
from aries_se_core_tasks.datetime.join_data_and_time_format import (
    Params as JoinDateAndTimeFormatParams,
)
from aries_se_core_tasks.datetime.join_data_and_time_format import run_join_date_and_time_format
from aries_se_core_tasks.frame.get_rows_by_condition import Params as GetRowsByConditionParams
from aries_se_core_tasks.frame.get_rows_by_condition import run_get_rows_by_condition
from aries_se_core_tasks.io.read.batch_producer import Params as BatchProducerParams
from aries_se_core_tasks.io.read.batch_producer import run_batch_producer
from aries_se_core_tasks.io.read.cloud.download_file import run_download_file
from aries_se_core_tasks.io.read.csv_file_splitter import Params as CsvSplitterParams
from aries_se_core_tasks.io.read.csv_file_splitter import run_csv_file_splitter
from aries_se_core_tasks.io.read.rename_csv_header import Params as RenameCsvHeaderParams
from aries_se_core_tasks.io.read.rename_csv_header import run_rename_csv_header
from aries_se_core_tasks.io.write.upload_file import Params as UploadFileParams
from aries_se_core_tasks.io.write.upload_file import run_upload_file
from aries_se_core_tasks.utilities.serializer import serializer
from aries_task_link.models import AriesTaskInput
from integration_generic_relational_tasks.restricted_list_transform.static import (
    RESTRICTED_LIST_FLOW_NAME,
    RestrictedListSourceColumns,
)
from integration_trades_tasks.order_blotter_to_restricted_list.input_schema import (
    OrderBlotterToRestrictedListAriesTaskInput,
)
from pandas._libs import Timedelta
from pathlib import Path
from se_core_tasks.core.core_dataclasses import CloudProviderEnum, FileSplitterResult
from se_core_tasks.utils.datetime import DatetimeFormat
from se_data_lake.cloud_utils import (
    get_bucket,
    get_cloud_provider_from_file_uri,
    get_cloud_provider_prefix,
)
from se_data_lake.lake_path import get_non_streamed_evented_custom_folder_file_path
from se_io_utils.tempfile_utils import tmp_directory
from se_trades_tasks.order.transformations.universal import (
    steeleye_universal_order_blotter_schema as sc,
)

logger = logging.getLogger(__name__)

FLOW_NAME = "order_blotter_to_restricted_list"


def order_blotter_to_restricted_list_flow(
    aries_task_input: AriesTaskInput,
    app_metrics_path: str,
    audit_path: str,
    result_path: str,
):
    """This flow works as an intermediate step between order blotter and
    restricted list.

    Whenever it is specified in an override for order blotter, batched files will be copied
    to trigger this flow.
    NB: max batch size need to be set the same (or higher) as order blotter as this flow only
    expects one batch after batch producer.

    From the copied CSV, it will generate an input file for restricted list workflow and
    upload it in the respective path.
    It's expected for this flow to always add on to the same restricted list instead of
    creating new ones for each execution.
    """
    # PRE PROCESSING #

    # Parse and validate AriesTaskInput parameters
    order_blotter_to_restricted_list_input: OrderBlotterToRestrictedListAriesTaskInput = (
        unpack_aries_task_input(
            aries_task_input=aries_task_input, model=OrderBlotterToRestrictedListAriesTaskInput
        )
    )

    # Get tenant workflow tenant config from postgres
    cached_tenant_workflow_config: addict.Dict = CachedTenantWorkflowAPIClient.get(
        stack_name=aries_task_input.workflow.stack,
        tenant_name=aries_task_input.workflow.tenant,
        workflow_name=aries_task_input.workflow.name,
    )

    # Get tenant workflow tenant config from postgres
    order_blotter_workflow_config: addict.Dict = CachedTenantWorkflowAPIClient.get(
        stack_name=aries_task_input.workflow.stack,
        tenant_name=aries_task_input.workflow.tenant,
        workflow_name="order_blotter",
    )

    streamed: bool = cached_tenant_workflow_config.workflow.streamed

    # Create local temporary directory to store intermediate files
    tmp_storage: str = tmp_directory().as_posix()

    # Determine Cloud Provider
    cloud_provider = get_cloud_provider_from_file_uri(
        file_uri=order_blotter_to_restricted_list_input.file_uri
    )
    cloud_provider_prefix = get_cloud_provider_prefix(value=cloud_provider)

    # Determine the Cloud Bucket of the tenant
    tenant_bucket_with_cloud_prefix: str = get_tenant_bucket(
        task_input=order_blotter_to_restricted_list_input,
        cloud_provider_prefix=cloud_provider_prefix,
    )

    # download file to local path
    local_file_path: str = run_download_file(
        file_url=order_blotter_to_restricted_list_input.file_uri
    )

    # Read the input CSV file, normalise its columns,
    # and convert null-like strings to real null values.
    # This Task was used in Swarm-Flows to produce multiple CSV files
    # but that behavior is not needed here
    # as we are already working with a chunk of the original input file,
    # thus we are always
    # getting the first and only element of the resulting list of FileSplitterResults.
    csv_splitter_result: FileSplitterResult = run_csv_file_splitter(
        streamed=streamed,
        params=CsvSplitterParams(
            detect_encoding=True,
            normalise_columns=True,
            audit_input_rows=False,
            drop_empty_rows=False,
            chunksize=order_blotter_workflow_config.max_batch_size,
        ),
        csv_path=local_file_path,
        realm=tenant_bucket_with_cloud_prefix,
        sources_dir=tmp_storage,
    )[0]

    # As some clients cannot provide CSV files with special characters in the header,
    # we need to rename the header columns to match the schema
    rename_csv_header_result: FileSplitterResult = run_rename_csv_header(
        file_path=csv_splitter_result,
        params=RenameCsvHeaderParams(
            detect_encoding=True,
            columns_map={
                "ISPAD": "ISPAD?",
                "ORDERDATE": "ORDERDATE(YYYYMMDD)",
                "ORDERDATEYYYYMMDD": "ORDERDATE(YYYYMMDD)",
                "ORDERTIME": "ORDERTIME(HH:MM:SS)",
                "ORDERTIME(HHMMSS)": "ORDERTIME(HH:MM:SS)",
                "ORDERTIMEHH:MM:SS": "ORDERTIME(HH:MM:SS)",
                "ORDERTIMEHHMMSS": "ORDERTIME(HH:MM:SS)",
                "TRADEDATE": "TRADEDATE(YYYYMMDD)",
                "TRADEDATEYYYYMMDD": "TRADEDATE(YYYYMMDD)",
                "TRADETIME": "TRADETIME(HH:MM:SS)",
                "TRADETIME(HHMMSS)": "TRADETIME(HH:MM:SS)",
                "TRADETIMEHH:MM:SS": "TRADETIME(HH:MM:SS)",
                "TRADETIMEHHMMSS": "TRADETIME(HH:MM:SS)",
                "UNDERLYINGINSTRUMENTISIN": "UNDERLYINGINSTRUMENTISIN/S",
                "UNDERLYINGINSTRUMENTISINS": "UNDERLYINGINSTRUMENTISIN/S",
            },
        ),
    )

    # Run the BatchProducer Task to produce a Pandas DataFrame from the extracted CSV chunk
    # The Task also enforces datatypes, creates missing columns and
    # audits missing/unnecessary/empty columns
    input_df: pd.DataFrame = run_batch_producer(
        streamed=streamed,
        params=BatchProducerParams(
            source_schema={
                sc.ISIN.normalized_column_name: sc.ISIN.column_data_type,
                sc.IS_PAD.normalized_column_name: sc.IS_PAD.column_data_type,
                sc.ORDER_DATE.normalized_column_name: sc.ORDER_DATE.column_data_type,
                sc.ORDER_TIME.normalized_column_name: sc.ORDER_TIME.column_data_type,
                sc.TRADE_DATE.normalized_column_name: sc.TRADE_DATE.column_data_type,
                sc.TRADE_ID.normalized_column_name: sc.TRADE_ID.column_data_type,
                sc.TRADE_TIME.normalized_column_name: sc.TRADE_TIME.column_data_type,
                sc.UNDERLYING_INSTRUMENT_SYMBOL.normalized_column_name: sc.UNDERLYING_INSTRUMENT_SYMBOL.column_data_type,  # noqa: E501
            },
            audit_null_columns=False,
        ),
        file_splitter_result=rename_csv_header_result,
        return_dataframe=True,
        app_metrics_path=app_metrics_path,
        audit_path=audit_path,
    )

    # LOGIC #

    # only keep columns where [Is PAD?] is True
    input_df = run_get_rows_by_condition(
        source_frame=input_df,
        params=GetRowsByConditionParams(
            query=(
                f"`{sc.IS_PAD.normalized_column_name}`.astype('str').str"
                f".fullmatch('y|yes|t|true|on|1',case=False,na=False)"
            )
        ),
    )

    if not input_df.empty:
        file_name_uuid = "PAD"

        target_df = create_restricted_list_frame(input_df=input_df, list_id=file_name_uuid)

        upload_csv_to_restricted_list_path(
            target_df=target_df,
            cloud_provider=cloud_provider,
            aries_task_input=aries_task_input,
            file_uri=order_blotter_to_restricted_list_input.file_uri,
            file_name_uuid=file_name_uuid,
            temp_storage=tmp_storage,
        )
    else:
        logger.info("No rows left after skip logic. No file will be uploaded")

    finish_flow(
        result_path=result_path,
        result_data={},
    )


@serializer
def create_restricted_list_frame(input_df: pd.DataFrame, list_id: str) -> pd.DataFrame:
    target_df = pd.DataFrame(index=input_df.index)

    # hardcoded
    target_df[RestrictedListSourceColumns.LIST_NAME.field] = "PAD"
    target_df[RestrictedListSourceColumns.LIST_ID.field] = list_id
    target_df[RestrictedListSourceColumns.LIST_TYPE.field] = "PAD"
    target_df[RestrictedListSourceColumns.PERMANENCY.field] = "Temporary"
    target_df[RestrictedListSourceColumns.IDENTIFIER_TYPE.field] = "ISIN"

    # direct mapping
    target_df[RestrictedListSourceColumns.EVENT_ID.field] = input_df[
        sc.TRADE_ID.normalized_column_name
    ]
    target_df[RestrictedListSourceColumns.IDENTIFIER.field] = input_df[
        sc.ISIN.normalized_column_name
    ].fillna(input_df[sc.UNDERLYING_INSTRUMENT_ISIN.normalized_column_name])

    # dates
    datetime_format = DatetimeFormat.DATETIME

    target_df[RestrictedListSourceColumns.DATETIME_WATCH_TO.field] = run_join_date_and_time_format(
        source_frame=input_df,
        params=JoinDateAndTimeFormatParams(
            source_date_attribute=sc.ORDER_DATE.normalized_column_name,
            source_time_attribute=sc.ORDER_TIME.normalized_column_name,
            target_attribute=RestrictedListSourceColumns.DATETIME_WATCH_TO.field,
            source_formats=["%Y%m%d%H:%M:%S.%fZ", "%Y%m%d%H:%M:%S.%f", "%Y%m%d%H:%M:%S"],
            target_format=datetime_format,
        ),
        skip_serializer=True,
    )[RestrictedListSourceColumns.DATETIME_WATCH_TO.field]

    target_df[RestrictedListSourceColumns.DATETIME_WATCH_FROM.field] = (
        pd.to_datetime(target_df[RestrictedListSourceColumns.DATETIME_WATCH_TO.field])
        - Timedelta(minutes=10, seconds=0.01)
    ).dt.strftime(datetime_format)

    return target_df


@serializer
def upload_csv_to_restricted_list_path(
    target_df: pd.DataFrame,
    cloud_provider: CloudProviderEnum,
    aries_task_input: AriesTaskInput,
    file_uri: str,
    file_name_uuid: str,
    temp_storage: str,
):
    file_path = Path(temp_storage).joinpath(Path(file_uri).name)

    target_df.to_csv(file_path.as_posix(), index=False)

    # Create the appropriate path to upload the files to
    upload_path_prefix = get_non_streamed_evented_custom_folder_file_path(
        workflow_name=RESTRICTED_LIST_FLOW_NAME,
        workflow_trace_id=aries_task_input.workflow.trace_id,
        workflow_start_timestamp=aries_task_input.workflow.start_timestamp,
        custom_folder="flow_trigger",
    )

    # Create CloudFile instance to be uploaded
    file_to_upload = CloudFile(
        file_path=file_path,
        bucket_name=get_bucket(file_uri),
        key_name=f"{upload_path_prefix}/{file_name_uuid}.csv",
        action=CloudAction.UPLOAD,
    )

    # Upload the file batches to cloud
    run_upload_file(
        upload_target=file_to_upload, cloud_provider=cloud_provider, params=UploadFileParams()
    )
