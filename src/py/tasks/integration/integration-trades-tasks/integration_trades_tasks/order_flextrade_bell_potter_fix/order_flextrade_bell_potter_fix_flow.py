import logging
import os
import pandas as pd
from addict import addict
from aries_config_cached_client.tenant_workflow import CachedTenantWorkflowAPIClient
from aries_se_core_tasks.aries.sink_file_audit.auditor_and_metrics_producer import (
    Metrics,
    RecordLevelAudit,
    run_auditor_and_metrics_producer,
)
from aries_se_core_tasks.aries.sink_file_audit.auditor_and_metrics_producer import (
    Params as AuditorAndMetricsProducerParams,
)
from aries_se_core_tasks.aries.sink_file_audit.static import (
    StandardAuditMessages,
)
from aries_se_core_tasks.aries.utility_tasks.finish_flow import (
    add_nested_params,
    finish_flow,
)
from aries_se_core_tasks.aries.utility_tasks.unpack_aries_task_input import unpack_aries_task_input
from aries_se_core_tasks.core.generic_app_metrics_enum import GenericAppMetricsEnum
from aries_se_core_tasks.frame.frame_column_manipulator import (  # type: ignore[attr-defined]
    Params as FrameColumnManipulatorParams,
)
from aries_se_core_tasks.frame.frame_column_manipulator import (
    run_frame_column_manipulator,
)
from aries_se_core_tasks.frame.frame_concatenator import (  # type: ignore[attr-defined]
    Params as FrameConcatenatorParams,
)
from aries_se_core_tasks.frame.frame_concatenator import run_frame_concatenator
from aries_se_core_tasks.frame.frame_splitter import (  # type: ignore[attr-defined]
    Params as FrameSplitterParams,
)
from aries_se_core_tasks.frame.frame_splitter import (
    run_frame_splitter,
)
from aries_se_core_tasks.frame.get_rows_by_condition import (  # type: ignore[attr-defined]
    Params as GetRowsByConditionParams,
)
from aries_se_core_tasks.frame.get_rows_by_condition import (
    run_get_rows_by_condition,
)
from aries_se_core_tasks.get_primary_transformations import (  # type: ignore[attr-defined]
    run_get_primary_transformations,
)
from aries_se_core_tasks.io.create_ndjson_path import create_ndjson_path
from aries_se_core_tasks.io.write.write_ndjson import run_write_ndjson
from aries_se_core_tasks.static import MetaModel
from aries_se_core_tasks.utilities.elasticsearch_utils import get_es_config, get_srp_es_config
from aries_se_trades_tasks.instrument.instrument_fallback import (  # type: ignore[attr-defined]
    Params as InstrumentFallbackParams,
)
from aries_se_trades_tasks.instrument.instrument_fallback import (
    run_instrument_fallback,
)
from aries_se_trades_tasks.instrument.link_instrument import (  # type: ignore[attr-defined]
    Params as LinkInstrumentParams,
)
from aries_se_trades_tasks.instrument.link_instrument import run_link_instrument
from aries_se_trades_tasks.meta.assign_meta_parent import (  # type: ignore[attr-defined]
    Params as MetaParentParams,
)
from aries_se_trades_tasks.meta.assign_meta_parent import run_assign_meta_parent
from aries_se_trades_tasks.order.best_execution import run_best_execution
from aries_se_trades_tasks.order.static import OrderWorkflowNames
from aries_se_trades_tasks.order.transformations.feed.order_flextrade_bell_potter_fix.static import (  # noqa: E501
    FlextradeBellPotterTempColumns,
)
from aries_se_trades_tasks.order_app_metrics_enum import OrderAppMetricsEnum
from aries_se_trades_tasks.party.link_parties import (  # type: ignore[attr-defined]
    Params as LinkPartiesParams,
)
from aries_se_trades_tasks.party.link_parties import run_link_parties
from aries_se_trades_tasks.party.party_fallback_with_lei_lookup import (  # type: ignore[attr-defined]
    Params as PartyFallbackParams,
)
from aries_se_trades_tasks.party.party_fallback_with_lei_lookup import (
    run_party_fallback_with_lei_lookup,
)
from aries_se_trades_tasks.remove_duplicates.remove_duplicate_newo import (  # type: ignore[attr-defined]
    Params as RemoveDuplicateNewoParams,
)
from aries_se_trades_tasks.remove_duplicates.remove_duplicate_newo import (
    run_remove_duplicate_newo,
)
from aries_task_link.models import AriesTaskInput
from distutils.util import strtobool
from integration_audit.auditor import AuditorStaticFields
from integration_trades_tasks.order_flextrade_bell_potter_fix.input_schema import (
    OrderFlextradeBellPotterFixAriesTaskInput,
)
from pydantic import BaseSettings, Field
from se_core_tasks.core.core_dataclasses import CloudProviderEnum
from se_core_tasks.frame.frame_column_manipulator import Action
from se_core_tasks.frame.frame_concatenator import OrientEnum
from se_elastic_schema.elastic_schema.core.steeleye_schema_model import SteelEyeSchemaBaseModelES8
from se_elastic_schema.models import Order
from se_elasticsearch.repository import get_repository_by_cluster_version
from se_elasticsearch.repository.elasticsearch6 import ElasticsearchRepository
from se_enums.elastic_search import EsActionEnum
from se_schema_meta import PARENT
from se_trades_tasks.order.party.static import PARTY_FILE_ID_FALLBACK_COL_MAPPING
from se_trades_tasks.order.static import ModelPrefix, OrderColumns, OrderTempColumns, add_prefix
from se_trades_tasks.order_and_tr.instrument.fallback.instrument_fallback import (
    FallbackInstrumentAttributes,
)
from se_trades_tasks.order_and_tr.static import INSTRUMENT_PATH, InstrumentFields
from typing import List, Optional, Type

# supress chained_assignment warning to avoid polluting the logs
pd.options.mode.chained_assignment = None

logger_ = logging.getLogger(__name__)

FETCH_MARKET_EOD_DATA: bool = bool(strtobool(os.getenv("FETCH_MARKET_EOD_DATA", "True")))

######################################################################################
##### TODO: If synthetic NEWOs are not required, remove the synthetic NEWO logic #####
######################################################################################


class OrderFlextradeBellPotterFixSettings(BaseSettings):
    # For Azure clients, this ENV VAR must be set to True to use Master Data
    # API instead of connecting directly to SRP
    srp_through_master_data: bool = Field(default=False, env="SRP_THROUGH_MASTER_DATA")


def order_flextrade_bell_potter_fix_flow(
    aries_task_input: AriesTaskInput,
    app_metrics_path: str,
    audit_path: str,
    result_path: str,
):
    """This flow now expects a pkl path generated from the batching logic from
    the file_splitter_by_criteria, along with the cache parquet uri.

    Note that despite some S3 hardcoded names, the
    flow is cloud-agnostic. The output of the Flow is potentially 2 ndjson
    files:

    - One with the Orders that the client provided
    - One with Synthetic NEWOs
    """

    # SETUP #
    env_config = OrderFlextradeBellPotterFixSettings()

    # Parse and validate AriesTaskInput parameters
    order_flextrade_bell_potter_fix_input: OrderFlextradeBellPotterFixAriesTaskInput = (
        unpack_aries_task_input(
            aries_task_input=aries_task_input, model=OrderFlextradeBellPotterFixAriesTaskInput
        )
    )

    # Get tenant workflow tenant config from PostGres
    cached_tenant_workflow_config: addict.Dict = CachedTenantWorkflowAPIClient.get(
        stack_name=aries_task_input.workflow.stack,
        tenant_name=aries_task_input.workflow.tenant,
        workflow_name=aries_task_input.workflow.name,
    )

    streamed: bool = cached_tenant_workflow_config.workflow.streamed
    tenant: str = aries_task_input.workflow.tenant

    # Determine Cloud Provider
    cloud_provider = CloudProviderEnum(cached_tenant_workflow_config.tenant.cloud)

    # Determine the Cloud Bucket of the tenant
    tenant_bucket_with_cloud_prefix: str = cached_tenant_workflow_config.tenant.lake_prefix

    # Instantiate the ElasticSearch client to access the tenant's data
    es_client_tenant: ElasticsearchRepository = get_repository_by_cluster_version(
        resource_config=get_es_config()
    )

    # Instantiate the ElasticSearch client to access the platform's reference data
    es_client_srp = instantiate_srp_es_client(
        srp_through_master_data=env_config.srp_through_master_data
    )

    # END SETUP #

    # BEGIN BUSINESS LOGIC #
    idx: int = order_flextrade_bell_potter_fix_input.batch
    batch_total: int = order_flextrade_bell_potter_fix_input.batch_total

    logger_.info(f"Processing chunk {idx + 1} of {batch_total}")

    batch_df = pd.read_pickle(order_flextrade_bell_potter_fix_input.file_uri)
    fix_cache_path = order_flextrade_bell_potter_fix_input.cache_parquet_uri

    orders_ndjson_path = _process_chunk(
        aries_task_input=aries_task_input,
        batch_df=batch_df,
        cloud_provider=cloud_provider,
        es_client_srp=es_client_srp,
        es_client_tenant=es_client_tenant,
        streamed=streamed,
        tenant=tenant,
        tenant_bucket_with_cloud_prefix=tenant_bucket_with_cloud_prefix,
        index=idx,
        skip_tenant_level_instrument_search=order_flextrade_bell_potter_fix_input.skip_tenant_level_instrument_search,
        app_metrics_path=app_metrics_path,
        audit_path=audit_path,
        orders_cache_path=fix_cache_path,
        file_uri=order_flextrade_bell_potter_fix_input.file_uri,
    )

    logger_.info(f"Processed chunk {idx + 1} of {batch_total}")

    if not orders_ndjson_path:
        logger_.info(f"No records to process in chunk {idx + 1} of {batch_total}")

    orders_reference: str = "orders"

    # NOTE: output key name must match the conductor definition
    output: dict = {
        orders_reference: add_nested_params(file_uri=None),
    }

    if orders_ndjson_path:
        output[orders_reference] = add_nested_params(
            file_uri=orders_ndjson_path,
            es_action=EsActionEnum.CREATE,
            data_model=Order.get_reference().get_qualified_reference(),
        )

    finish_flow(
        result_path=result_path,
        result_data=output,
    )


def _process_chunk(
    aries_task_input: AriesTaskInput,
    batch_df: pd.DataFrame,
    cloud_provider: CloudProviderEnum,
    es_client_srp: ElasticsearchRepository,
    es_client_tenant: ElasticsearchRepository,
    streamed: bool,
    tenant: str,
    tenant_bucket_with_cloud_prefix: str,
    index: int,
    skip_tenant_level_instrument_search: bool,
    orders_cache_path: str,
    file_uri: str,
    app_metrics_path: Optional[str],
    audit_path: Optional[str],
):
    orders_ndjson_path: Optional[str] = None

    # Populate the majority of the target fields in a centralized transformations class
    order_mappings_df = run_get_primary_transformations(
        source_frame=batch_df,
        flow=OrderWorkflowNames.ORDER_FLEXTRADE_BELL_POTTER_FIX,
        tenant=tenant,
        realm=tenant_bucket_with_cloud_prefix,
        app_metrics_path=app_metrics_path,
        audit_path=audit_path,
        es_client=es_client_tenant,
        orders_cache_path=orders_cache_path,
        file_uri=file_uri,
    )

    # Populate the `&parent` field for OrderStates
    meta_parent_df = run_assign_meta_parent(
        source_frame=order_mappings_df,
        params=MetaParentParams(
            parent_model_attribute=add_prefix(
                prefix=ModelPrefix.ORDER, attribute=OrderColumns.META_MODEL
            ),
            parent_attributes_prefix=ModelPrefix.ORDER_DOT,
            target_attribute=add_prefix(prefix=ModelPrefix.ORDER_STATE, attribute=PARENT),
        ),
        app_metrics_path=app_metrics_path,
        audit_path=audit_path,
    )

    # Link identifiers built in InstrumentIdentifier from input instrument
    # data with SRP and tenant instrument data
    link_instrument_df = run_link_instrument(
        source_frame=order_mappings_df,
        params=LinkInstrumentParams(
            identifiers_path=OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT,
            asset_class_attribute="asset_class_attribute",
            currency_attribute="currency_attribute",
            venue_attribute="venue_attribute",
            skip_tenant_level_instrument_search=skip_tenant_level_instrument_search,
        ),
        tenant=aries_task_input.workflow.tenant,
        es_client_srp=es_client_srp,
        es_client_tenant=es_client_tenant,
    )

    # Link identifiers built in BlotterPartyIdentifiers from
    # input party data with tenant MyMarket data
    link_parties_df = run_link_parties(
        tenant=aries_task_input.workflow.tenant,
        source_frame=order_mappings_df,
        params=LinkPartiesParams(identifiers_path=OrderColumns.MARKET_IDENTIFIERS_PARTIES),
        es_client=es_client_tenant,
        app_metrics_path=app_metrics_path,
        audit_path=audit_path,
    )

    party_fallback_input = run_frame_concatenator(
        transformed_df=order_mappings_df,
        link_parties_df=link_parties_df,
        params=FrameConcatenatorParams(orient=OrientEnum.horizontal),
        app_metrics_path=app_metrics_path,
        audit_path=audit_path,
    )

    # Create Party records embedded in the Orders for records
    # where LinkParties did not produce any hits
    party_fallback_df = run_party_fallback_with_lei_lookup(
        source_frame=party_fallback_input,
        params=PartyFallbackParams(),
    )

    instrument_fallback_input = run_frame_concatenator(
        transformed_df=order_mappings_df,
        link_instrument_df=link_instrument_df,
        params=FrameConcatenatorParams(orient=OrientEnum.horizontal),
        app_metrics_path=app_metrics_path,
        audit_path=audit_path,
    )

    # Create Instrument records embedded in the Orders for records
    # where LinkInstrument did not produce any hits
    instrument_fallback_df = run_instrument_fallback(
        source_frame=instrument_fallback_input,
        params=InstrumentFallbackParams(
            market_instrument_identifiers_attribute=OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT,
            instrument_fields_map=[
                FallbackInstrumentAttributes(
                    source_field="instrument_classification_attribute",
                    target_field=InstrumentFields.INSTRUMENT_CLASSIFICATION,
                ),
                FallbackInstrumentAttributes(
                    source_field=FlextradeBellPotterTempColumns.INSTRUMENT_FULL_NAME,
                    target_field=InstrumentFields.INSTRUMENT_FULL_NAME,
                ),
                FallbackInstrumentAttributes(
                    source_field=FlextradeBellPotterTempColumns.INSTRUMENT_EXT_ALTERNATIVE_INSTRUMENT_ID,
                    target_field=InstrumentFields.EXT_ALTERNATIVE_INSTRUMENT_ID,
                ),
                FallbackInstrumentAttributes(
                    source_field=FlextradeBellPotterTempColumns.INSTRUMENT_EXT_UNIQUE_INSTRUMENT_ID,
                    target_field=InstrumentFields.EXT_INSTRUMENT_UNIQUE_ID,
                ),
                FallbackInstrumentAttributes(
                    source_field="venue_attribute",
                    target_field=InstrumentFields.EXT_VENUE_NAME,
                ),
                FallbackInstrumentAttributes(
                    source_field=FlextradeBellPotterTempColumns.INSTRUMENT_IS_CREATED_THROUGH_FALLBACK,
                    target_field=InstrumentFields.IS_CREATED_THROUGH_FALLBACK,
                ),
            ],
            str_to_bool_dict={
                "true": True,
                "false": False,
            },
            cfi_and_bestex_from_instrument_classification=True,
            derived_asset_class_attribute="asset_class_attribute",
        ),
        app_metrics_path=app_metrics_path,
        audit_path=audit_path,
    )

    # Concat all relevant DataFrames and discard temporary
    # columns that must not be part of the final result
    aux_df = run_frame_concatenator(
        order_mappings_df=order_mappings_df,
        meta_parent_df=meta_parent_df,
        party_fallback_df=party_fallback_df,
        instrument_fallback_df=instrument_fallback_df,
        params=FrameConcatenatorParams(
            orient=OrientEnum.horizontal,
            drop_columns=[
                FlextradeBellPotterTempColumns.ASSET_CLASS,
                OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT,
                OrderColumns.MARKET_IDENTIFIERS_PARTIES,
                OrderColumns.META_MODEL,
                FlextradeBellPotterTempColumns.INSTRUMENT_FULL_NAME,
                FlextradeBellPotterTempColumns.INSTRUMENT_CLASSIFICATION,
                FlextradeBellPotterTempColumns.INSTRUMENT_EXT_ALTERNATIVE_INSTRUMENT_ID,
                FlextradeBellPotterTempColumns.INSTRUMENT_EXT_UNIQUE_INSTRUMENT_ID,
                FlextradeBellPotterTempColumns.INSTRUMENT_IS_CREATED_THROUGH_FALLBACK,
                "asset_class_attribute",
                "bbg_figi_id_attribute",
                "eurex_id_attribute",
                "currency_attribute",
                "expiry_date_attribute",
                "interest_rate_start_date_attribute",
                "isin_attribute",
                "notional_currency_1_attribute",
                "notional_currency_2_attribute",
                "option_strike_price_attribute",
                "option_type_attribute",
                "swap_near_leg_date_attribute",
                "underlying_index_name_attribute",
                "underlying_index_name_leg_2_attribute",
                "underlying_index_series_attribute",
                "underlying_index_term_attribute",
                "underlying_index_term_value_attribute",
                "underlying_index_version_attribute",
                "underlying_isin_attribute",
                "underlying_symbol_attribute",
                "underlying_symbol_expiry_code_attribute",
                "underlying_index_term_leg_2_attribute",
                "underlying_index_term_value_leg_2_attribute",
                "venue_attribute",
                "venue_financial_instrument_short_name_attribute",
                "instrument_classification_attribute",
            ]
            + list(PARTY_FILE_ID_FALLBACK_COL_MAPPING.values()),
        ),
        app_metrics_path=app_metrics_path,
        audit_path=audit_path,
    )

    # Split the columns pertaining to Orders to a separate DataFrame
    order_records_df = run_frame_splitter(
        source_frame=aux_df,
        params=FrameSplitterParams(except_prefix=ModelPrefix.ORDER_STATE_DOT, strip_prefix=True),
        app_metrics_path=app_metrics_path,
        audit_path=audit_path,
    )

    # Strip the Order prefix
    parsed_order_records_df = run_frame_column_manipulator(
        source_frame=order_records_df,
        params=FrameColumnManipulatorParams(action=Action.strip, prefix=ModelPrefix.ORDER_DOT),
        app_metrics_path=app_metrics_path,
        audit_path=audit_path,
    )

    # Split the columns pertaining to OrderStates to a separate DataFrame
    order_state_records_df = run_frame_splitter(
        source_frame=aux_df,
        params=FrameSplitterParams(except_prefix=ModelPrefix.ORDER_DOT, strip_prefix=True),
        app_metrics_path=app_metrics_path,
        audit_path=audit_path,
    )

    # Strip the OrderState prefix
    parsed_order_state_records_df = run_frame_column_manipulator(
        source_frame=order_state_records_df,
        params=FrameColumnManipulatorParams(
            action=Action.strip, prefix=ModelPrefix.ORDER_STATE_DOT
        ),
        app_metrics_path=app_metrics_path,
        audit_path=audit_path,
    )

    # Concat a final DataFrame that contains all Order +
    # OrderState records without any temporary columns/prefixes
    orders_and_order_states_df = run_frame_concatenator(
        parsed_order_records_df=parsed_order_records_df,
        parsed_order_state_records_df=parsed_order_state_records_df,
        params=FrameConcatenatorParams(
            orient=OrientEnum.vertical, reset_index=True, drop_index=True
        ),
        app_metrics_path=app_metrics_path,
        audit_path=audit_path,
    )

    # In the below GetRowsByCondition we'll drop all records that are missing
    # executionDetails.orderStatus Before dropping them, we must audit them to
    # ensure that we're auditing the input count correctly.
    # NEWO_IN_FILE is key here as we don't want to audit synthetic Orders
    # that were created as part of the flow and that must be discarded.
    query = (
        f"`{OrderColumns.EXECUTION_DETAILS_ORDER_STATUS}`.isnull() and "
        f"~`{OrderTempColumns.NEWO_IN_FILE}`"
    )
    run_auditor_and_metrics_producer(
        source_frame=orders_and_order_states_df,
        params=AuditorAndMetricsProducerParams(
            record_level_audits=[
                RecordLevelAudit(
                    query=query,
                    status_message="Invalid Record since it's missing order status",
                    audit_field=AuditorStaticFields.ERRORED,
                    meta_model=Order,
                )
            ],
            metrics=[
                Metrics(
                    query=query,
                    field=GenericAppMetricsEnum.ERRORED_COUNT,
                )
            ],
            models=[Order],
        ),
        streamed=streamed,
        cloud_provider=cloud_provider,
        app_metrics_path=app_metrics_path,
        audit_path=audit_path,
    )

    # Exclude all records that do not have a value in the "executionDetails.orderStatus" field
    # Such records are either missing from the input data column `ORDERSTATUS` or they have
    # an invalid Order Status which is not part of the map defined in the PrimaryTransformations
    filtered_orders_and_order_states_df = run_get_rows_by_condition(
        source_frame=orders_and_order_states_df,
        params=GetRowsByConditionParams(query="`executionDetails.orderStatus`.notnull()"),
        app_metrics_path=app_metrics_path,
        audit_path=audit_path,
    )

    # Remove duplicate NEWOs from dataset (i.e. synthetic NEWOs that were created unnecessarily)
    deduplicated_data_df = run_remove_duplicate_newo(
        source_frame=filtered_orders_and_order_states_df,
        params=RemoveDuplicateNewoParams(
            newo_in_file_col=OrderTempColumns.NEWO_IN_FILE,
            drop_newo_in_file_col=True,
            query_elastic_for_existing_newo=True,
        ),
        tenant=aries_task_input.workflow.tenant,
        es_client=es_client_tenant,
        streamed=streamed,
        cloud_provider=cloud_provider,
        audit_path=audit_path,
        app_metrics_path=app_metrics_path,
    )

    best_execution_df = run_best_execution(
        source_frame=deduplicated_data_df,
        es_client=es_client_tenant,
        streamed=streamed,
        fetch_market_eod_data=FETCH_MARKET_EOD_DATA,
        app_metrics_path=app_metrics_path,
        audit_path=audit_path,
    )

    # Note that we are not discarding the `&parent` column here
    # this can be propagated to the ApplyMeta Conductor Task, which will reuse it
    final_result_df = run_frame_concatenator(
        best_execution_df=best_execution_df,
        deduplicated_data_df=deduplicated_data_df,
        params=FrameConcatenatorParams(
            orient=OrientEnum.horizontal, drop_columns=[OrderColumns.META_MODEL]
        ),
        app_metrics_path=app_metrics_path,
        audit_path=audit_path,
    )

    # INDEX Orders
    orders_df = run_get_rows_by_condition(
        source_frame=final_result_df,
        params=GetRowsByConditionParams(
            query=f"(`{OrderColumns.EXECUTION_DETAILS_ORDER_STATUS}`.astype"
            f"('str').str.upper() == 'NEWO'"
            f" & `{OrderColumns.IS_SYNTHETIC}`.astype('str').str.lower() == 'false')"
            f" | (`{OrderColumns.EXECUTION_DETAILS_ORDER_STATUS}`.str.upper() != 'NEWO')"
            # noqa: E501
        ),
    )

    if not orders_df.empty:
        _audit_fallbacks(
            df=orders_df.frame(),
            col_name=INSTRUMENT_PATH,
            fallback_key="isCreatedThroughFallback",
            status_message=StandardAuditMessages.INSTRUMENT_THROUGH_FALLBACK,
            meta_model=Order,
            models=[Order],
            streamed=streamed,
            cloud_provider=cloud_provider,
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        orders_ndjson_path = create_ndjson_path(
            tenant_bucket=tenant_bucket_with_cloud_prefix,
            aries_task_input=aries_task_input,
            model=MetaModel.ORDER,
            suffix=f"{index}",
        )

        run_write_ndjson(
            source_serializer_result=orders_df,
            output_filepath=orders_ndjson_path,
            audit_output=True,
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

    return orders_ndjson_path


def instantiate_srp_es_client(srp_through_master_data: bool):
    if srp_through_master_data:
        return None

    es_client_srp: ElasticsearchRepository = get_repository_by_cluster_version(
        resource_config=get_srp_es_config()
    )

    return es_client_srp


def _audit_fallbacks(
    df: pd.DataFrame,
    col_name: str,
    fallback_key: str,
    status_message: str,
    meta_model: Type[SteelEyeSchemaBaseModelES8],
    models: List[Type[SteelEyeSchemaBaseModelES8]],
    streamed: bool,
    cloud_provider: CloudProviderEnum,
    app_metrics_path: Optional[str],
    audit_path: Optional[str],
):
    instruments_created_through_fallback = (
        df.loc[:, col_name].str.get(fallback_key).fillna(False)  # type: ignore
    )

    if any(instruments_created_through_fallback):
        run_auditor_and_metrics_producer(
            source_frame=df.loc[instruments_created_through_fallback, :],
            params=AuditorAndMetricsProducerParams(
                record_level_audits=[
                    RecordLevelAudit(
                        query="index==index",
                        status_message=status_message,
                        meta_model=meta_model,
                    )
                ],
                metrics=[
                    Metrics(
                        query="index==index",
                        field=OrderAppMetricsEnum.SYNTHETIC_INSTRUMENTS_COUNT,
                    )
                ],
                models=models,
            ),
            streamed=streamed,
            cloud_provider=cloud_provider,
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )
