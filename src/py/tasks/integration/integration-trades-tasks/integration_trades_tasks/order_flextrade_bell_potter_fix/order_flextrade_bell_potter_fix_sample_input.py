import datetime
import nanoid
from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput


# AWS ES8 input
def sample_input() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        trace_id=nanoid.generate(size=8),
        name="order_flextrade_bell_potter_fix",
        stack="uat-shared-steeleye",
        tenant="integration",
        start_timestamp=datetime.datetime(2024, 10, 29),
    )

    input_param = IOParamFieldSet(
        **{
            "params": {
                "file_uri": "s3://integration.uat.steeleye.co/aries/ingest/order_flextrade_bell_potter_fix/2024/10/29/hf89234g5f9378rf9/file_splitter_by_criteria/T_batch_0.pkl",
                "batch": 0,
                "batch_total": 2,
                "cache_parquet_uri": "s3://integration.uat.steeleye.co/aries/ingest/order_flextrade_bell_potter_fix/2024/10/29/hf89234g5f9378rf9/file_splitter_by_criteria/fix_cache.parquet",
            }
        }
    )

    task = TaskFieldSet(name="order_flextrade_bell_potter_fix", version="latest", success=False)

    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)
