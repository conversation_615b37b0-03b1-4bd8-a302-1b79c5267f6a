import logging
from aries_config_api_httpschema.mifir_scheduler_config import ScheduleTypeEnum
from aries_config_cached_client.mifir_scheduler_config import CachedMifirSchedulerConfigAPIClient
from aries_se_core_tasks.aries.utility_tasks.finish_flow import finish_flow
from aries_se_core_tasks.tr.arm_no_response_email_task import run_arm_no_response_email_task
from aries_task_link.models import AriesTaskInput
from typing import Optional

logger = logging.getLogger(__name__)


def tr_arm_no_response_email_flow(
    aries_task_input: AriesTaskInput,
    app_metrics_path: Optional[str] = None,
    audit_path: Optional[str] = None,
    result_path: str = "result.json",
):
    # Unpack input events
    tenant: str = aries_task_input.workflow.tenant
    env: str = aries_task_input.workflow.stack.split("-")[0]
    realm: str = f"{tenant}.{env}.steeleye.co" if env != "prod" else f"{tenant}.steeleye.co"

    tr_no_response_arm_configuration = dict()
    try:
        # Fetch TR No Arm Response Config
        tr_no_response_arm_configuration = CachedMifirSchedulerConfigAPIClient.get_config(
            stack_name=aries_task_input.workflow.stack,
            tenant_name=tenant,
            enabled=True,
            schedule_type=ScheduleTypeEnum.notify_on_no_arm_response.value,
        )
        logger.info(
            f"TR {ScheduleTypeEnum.notify_on_no_arm_response.value} config for {tenant} is "
            f"\n {tr_no_response_arm_configuration}"
        )
    except Exception:
        logger.warning(
            f"Could not fetch {ScheduleTypeEnum.notify_on_no_arm_response.value} config for {realm}"
        )

    # Run core task to send tr-arm-no-response-email.
    # This sends the notification if the user opted for it.
    run_arm_no_response_email_task(
        realm=realm,
        tenant=tenant,
        tr_no_arm_response_config=tr_no_response_arm_configuration,
        app_metrics_path=app_metrics_path,
        audit_path=audit_path,
    )

    finish_flow(
        result_path=result_path,
        result_data={},
    )
