import datetime
from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput


def sample_input() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        trace_id="test_instrument_mapper",
        name="order_blotter",
        stack="dev-blue",
        tenant="pinafore",
        start_timestamp=datetime.datetime.utcnow(),
    )
    input_param = IOParamFieldSet(
        params=dict(
            file_uri="s3://pinafore.dev.steeleye.co/aries/ingest/order_blotter/2023/10/10/optimized_l_i_4/elastic_connector/eb6dfa675595348f7d35d4515f571d06b2d55502335fa035e6077cde79e25e4c___raw_success.ndjson",
        )
    )
    task = TaskFieldSet(name="instrument_mapper", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)
