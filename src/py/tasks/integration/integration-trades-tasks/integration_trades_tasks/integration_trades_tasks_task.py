import logging
from aries_task_link.models import AriesTaskInput
from aries_task_link.task import aries_task
from integration_trades_tasks.task_map import resolve_task_func
from pydantic import BaseSettings, Field

log = logging.getLogger(__name__)


class IntegrationTradesTaskSettings(BaseSettings):
    task_name: str = Field(env="TASK_NAME")


@aries_task()
def integration_trades_tasks_run(aries_task_input: AriesTaskInput):
    _config = IntegrationTradesTaskSettings()
    task_func = resolve_task_func(task_name=_config.task_name)

    log.info("Executing task: %s", _config.task_name)
    return task_func(aries_task_input)
