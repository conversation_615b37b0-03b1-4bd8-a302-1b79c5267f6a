from aries_se_trades_tasks.order.transformations.feed.order_aladdin_v2.static import (
    INPUT_COLUMNS_TYPE_MAP,
    ORDER_ALADDIN_V2_MARKET_SIDE_FEED_NAME,
)
from integration_trades_tasks.order_aladdin_v2.order_aladdin_v2_flow import Default<PERSON>laddinV2
from se_elastic_schema.static.reference import OrderRecordType


class MarketSideOrdersAladdinV2(DefaultAladdinV2):
    def _get_input_columns_type_map(self):
        return INPUT_COLUMNS_TYPE_MAP

    def _get_flow_name(self):
        return ORDER_ALADDIN_V2_MARKET_SIDE_FEED_NAME

    def _get_record_type(self):
        return OrderRecordType.MARKET_SIDE.value.replace(" ", "_").lower()
