import logging
from benchmark_mode import benchmark
from integration_trades_tasks.integration_trades_tasks_task import integration_trades_tasks_run
from integration_trades_tasks.tr_bbg_emsi_orders.tr_bbg_emsi_orders_sample_input import (  # noqa E501
    sample_input,
)

logger = logging.getLogger("tr_bbg_emsi_orders")


@benchmark
def main():
    logger.info("Starting execution...")
    output = integration_trades_tasks_run(aries_task_input=sample_input())
    logger.info(f"Finished executing with output {output}")


if __name__ == "__main__":
    main()
