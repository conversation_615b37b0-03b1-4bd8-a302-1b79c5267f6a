import logging
from aries_task_link.models import AriesTaskInput, AriesTaskResult
from integration_trades_tasks.tr_bbg_emsi_orders.app_metrics_template import APP_METRICS
from integration_trades_tasks.tr_bbg_emsi_orders.flow_overrides.chelverton import (
    ChelvertonTrBBGEMSIOrders,
)
from integration_trades_tasks.tr_bbg_emsi_orders.tr_bbg_emsi_orders_flow import (
    TrBBGEMSIOrders,
    tr_bbg_emsi_orders_flow,
)
from integration_wrapper.integration_aries_task import IntegrationAriesTask

logger = logging.getLogger("tr_bbg_emsi_orders_flow")


def tr_bbg_emsi_orders_run(aries_task_input: AriesTaskInput) -> AriesTaskResult:
    integration = IntegrationAriesTask(
        aries_task_input=aries_task_input,
        app_metrics_template=APP_METRICS,
    )

    flow_overrides_map = {
        "chelverton": ChelvertonTrBBGEMSIOrders,
    }

    tenant = aries_task_input.workflow.tenant
    flow_override_class = flow_overrides_map.get(tenant, TrBBGEMSIOrders)

    return integration.execute(
        flow=tr_bbg_emsi_orders_flow, flow_override_class=flow_override_class
    )
