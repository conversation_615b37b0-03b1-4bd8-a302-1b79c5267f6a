import pandas as pd
import shutil
from addict import addict
from aries_config_cached_client.tenant_workflow import CachedTenantWorkflowAPIClient
from aries_se_core_tasks.aries.sink_file_audit.auditor_and_metrics_producer import (
    Metrics,
    RecordLevelAudit,
    run_auditor_and_metrics_producer,
)
from aries_se_core_tasks.aries.sink_file_audit.auditor_and_metrics_producer import (
    Params as AuditorAndMetricsProducerParams,
)
from aries_se_core_tasks.aries.sink_file_audit.static import (
    StandardAuditMessages,
)
from aries_se_core_tasks.aries.utility_tasks.finalize_task import (
    create_path_and_upload_model_results,
)
from aries_se_core_tasks.aries.utility_tasks.finish_flow import add_nested_params, finish_flow
from aries_se_core_tasks.aries.utility_tasks.get_tenant_bucket import get_tenant_bucket
from aries_se_core_tasks.aries.utility_tasks.unpack_aries_task_input import unpack_aries_task_input
from aries_se_core_tasks.core.core_dataclasses import SerializerResult
from aries_se_core_tasks.frame.frame_column_manipulator import (  # type: ignore[attr-defined]
    Params as FrameColumnManipulatorParams,
)
from aries_se_core_tasks.frame.frame_column_manipulator import run_frame_column_manipulator
from aries_se_core_tasks.frame.frame_concatenator import (  # type: ignore[attr-defined]
    Params as FrameConcatenatorParams,
)
from aries_se_core_tasks.frame.frame_concatenator import run_frame_concatenator
from aries_se_core_tasks.frame.frame_splitter import (  # type: ignore[attr-defined]
    Params as FrameSplitterParams,
)
from aries_se_core_tasks.frame.frame_splitter import run_frame_splitter
from aries_se_core_tasks.frame.get_rows_by_condition import (  # type: ignore[attr-defined]
    Params as GetRowsByConditionParams,
)
from aries_se_core_tasks.frame.get_rows_by_condition import run_get_rows_by_condition
from aries_se_core_tasks.get_primary_transformations import (  # type: ignore[attr-defined]
    run_get_primary_transformations,
)
from aries_se_core_tasks.io.create_ndjson_path import create_ndjson_path
from aries_se_core_tasks.io.read.batch_producer import (  # type: ignore[attr-defined]
    Params as BatchProducerParams,
)
from aries_se_core_tasks.io.read.batch_producer import (  # type: ignore[attr-defined]
    run_batch_producer,
)
from aries_se_core_tasks.io.read.cloud.download_file import run_download_file
from aries_se_core_tasks.io.read.csv_file_splitter import (  # type: ignore[attr-defined]
    Params as CsvSplitterParams,
)
from aries_se_core_tasks.io.read.csv_file_splitter import run_csv_file_splitter
from aries_se_core_tasks.io.write.write_ndjson import run_write_ndjson
from aries_se_core_tasks.static import MetaModel
from aries_se_core_tasks.utilities.elasticsearch_utils import get_es_config, get_srp_es_config
from aries_se_trades_tasks.instrument.instrument_fallback import (  # type: ignore[attr-defined]
    Params as InstrumentFallbackParams,
)
from aries_se_trades_tasks.instrument.instrument_fallback import run_instrument_fallback
from aries_se_trades_tasks.instrument.link_instrument import (  # type: ignore[attr-defined]
    Params as LinkInstrumentParams,
)
from aries_se_trades_tasks.instrument.link_instrument import run_link_instrument
from aries_se_trades_tasks.meta.assign_meta_parent import (  # type: ignore[attr-defined]
    Params as MetaParentParams,
)
from aries_se_trades_tasks.meta.assign_meta_parent import run_assign_meta_parent
from aries_se_trades_tasks.order.best_execution import run_best_execution
from aries_se_trades_tasks.order.static import OrderWorkflowNames
from aries_se_trades_tasks.order_app_metrics_enum import OrderAppMetricsEnum
from aries_se_trades_tasks.orders_and_tr.quarantine import (
    run_quarantined_records,
)
from aries_se_trades_tasks.orders_and_tr.transformations.feed.order_tr_fidessa_eod.static import (
    SOURCE_SCHEMA,
    AuxiliarColumns,
    MatchType,
    TempColumns,
)
from aries_se_trades_tasks.party.link_parties import (  # type: ignore[attr-defined]
    Params as LinkPartiesParams,
)
from aries_se_trades_tasks.party.link_parties import run_link_parties
from aries_se_trades_tasks.party.party_fallback_with_lei_lookup import (  # type: ignore[attr-defined]
    Params as PartyFallbackParams,
)
from aries_se_trades_tasks.party.party_fallback_with_lei_lookup import (
    run_party_fallback_with_lei_lookup,
)
from aries_se_trades_tasks.remove_duplicates.remove_duplicate_newo import (  # type: ignore[attr-defined]
    Params as RemoveDuplicateNewoParams,
)
from aries_se_trades_tasks.remove_duplicates.remove_duplicate_newo import run_remove_duplicate_newo
from aries_se_trades_tasks.tr.eligibility_assessor.eligibility_assessor import (
    run_eligibility_assessor,
)
from aries_se_trades_tasks.tr.static import RTS22TransactionWorkflowNames
from aries_task_link.models import AriesTaskInput
from integration_audit.auditor import upsert_audit
from integration_trades_tasks.order_tr_fidessa_eod.abstract_order_tr_fidessa_eod_flow import (
    AbstractOrderTrFidessaEOD,
)
from integration_trades_tasks.order_tr_fidessa_eod.input_schema import (
    OrderTrFidessaEODAriesTaskInput,
)
from pathlib import Path
from pydantic import BaseSettings, Field
from se_core_tasks.core.core_dataclasses import CloudProviderEnum, FileSplitterResult
from se_core_tasks.frame.frame_column_manipulator import Action
from se_core_tasks.frame.frame_concatenator import OrientEnum
from se_data_lake.cloud_utils import get_cloud_provider_from_file_uri, get_cloud_provider_prefix
from se_elastic_schema.elastic_schema.core.steeleye_schema_model import SteelEyeSchemaBaseModelES8
from se_elastic_schema.models import (
    Order,
    QuarantinedRTS22Transaction,
    RTS22Transaction,
    SinkRecordAudit,
)
from se_elasticsearch.repository import get_repository_by_cluster_version
from se_elasticsearch.repository.elasticsearch6 import ElasticsearchRepository
from se_enums.elastic_search import EsActionEnum
from se_io_utils.tempfile_utils import tmp_directory
from se_schema_meta import PARENT
from se_trades_tasks.order.party.static import PARTY_FILE_ID_FALLBACK_COL_MAPPING
from se_trades_tasks.order.static import ModelPrefix, OrderColumns, OrderTempColumns, add_prefix
from se_trades_tasks.order_and_tr.instrument.fallback.instrument_fallback import (
    FallbackInstrumentAttributes,
)
from se_trades_tasks.order_and_tr.static import INSTRUMENT_PATH, InstrumentFields
from se_trades_tasks.tr.static import RTS22TransactionColumns
from typing import List, Tuple, Type


class OrderTrFidessaEODSettings(BaseSettings):
    srp_through_master_data: bool = Field(default=False, env="SRP_THROUGH_MASTER_DATA")


class OrderTrFidessaEOD(AbstractOrderTrFidessaEOD):
    def run_flow(
        self,
        aries_task_input: AriesTaskInput,
        app_metrics_path: str,
        audit_path: str,
        result_path: str,
    ):
        # BEGIN SETUP #
        env_config = OrderTrFidessaEODSettings()

        # Parse and validate AriesTaskInput parameters
        order_tr_fidessa_eod_input: OrderTrFidessaEODAriesTaskInput = unpack_aries_task_input(
            aries_task_input=aries_task_input, model=OrderTrFidessaEODAriesTaskInput
        )

        # Get tenant workflow tenant config from postgres
        cached_tenant_workflow_config: addict.Dict = CachedTenantWorkflowAPIClient.get(
            stack_name=aries_task_input.workflow.stack,
            tenant_name=aries_task_input.workflow.tenant,
            workflow_name=aries_task_input.workflow.name,
        )

        streamed: bool = cached_tenant_workflow_config.workflow.streamed
        tenant: str = aries_task_input.workflow.tenant

        # Create local temporary directory to store intermediate files
        tmp_storage: str = tmp_directory().as_posix()

        # Determine Cloud Provider
        cloud_provider = get_cloud_provider_from_file_uri(
            file_uri=order_tr_fidessa_eod_input.file_uri
        )
        cloud_provider_prefix = get_cloud_provider_prefix(value=cloud_provider)

        # Determine the Cloud Bucket of the tenant
        tenant_bucket_with_cloud_prefix: str = get_tenant_bucket(
            task_input=order_tr_fidessa_eod_input, cloud_provider_prefix=cloud_provider_prefix
        )

        es_client_tenant: ElasticsearchRepository = get_repository_by_cluster_version(
            resource_config=get_es_config()
        )

        es_client_srp: ElasticsearchRepository = self.instantiate_srp_es_client(
            srp_through_master_data=env_config.srp_through_master_data
        )
        # END SETUP #

        # BEGIN PRE-PROCESSING #

        # download file to local path
        local_file_path: str = run_download_file(file_url=order_tr_fidessa_eod_input.file_uri)

        # Read the input CSV file, normalise its columns,
        # and convert null-like "string"s to real null values.
        # This Task was used in Swarm-Flows to produce multiple CSV files
        # but that behavior is not needed here as we are already working
        # with a chunk of the original input file, thus we are always
        # getting the first and only element of the resulting list of FileSplitterResults.
        csv_splitter_result: FileSplitterResult = run_csv_file_splitter(
            streamed=streamed,
            params=CsvSplitterParams(
                chunksize=cached_tenant_workflow_config.max_batch_size,
                detect_encoding=True,
            ),
            csv_path=local_file_path,
            realm=tenant_bucket_with_cloud_prefix,
            sources_dir=tmp_storage,
            audit_input_rows=True,
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )[0]

        # Run the BatchProducer Task to produce a Pandas DataFrame from the extracted CSV chunk
        # The Task also enforces datatypes, creates missing columns and
        # audits missing/unnecessary/empty columns
        input_df: SerializerResult = run_batch_producer(
            file_splitter_result=csv_splitter_result,
            params=BatchProducerParams(
                source_schema=SOURCE_SCHEMA,
                remove_unknown_columns=True,
                audit_null_columns=False,
            ),
            return_dataframe=True,
            streamed=streamed,
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # Used for Oppenheimer override
        input_df = self.skip_logic(
            input_df=input_df,
            streamed=streamed,
            cloud_provider=cloud_provider,
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        orders_df = pd.DataFrame()
        ## Currently there's never any synthetic new in this feed, leaving implementation
        ## here in case it's needed in the future
        # synthetic_newo_df = pd.DataFrame()
        rts22_transaction_df = pd.DataFrame()
        sink_record_audit_rts22_df = pd.DataFrame()
        quarantined_rts22_transaction_df = pd.DataFrame()

        if input_df.empty:
            upsert_audit(
                audit_path=audit_path,
                streamed=streamed,
                workflow_status=[
                    f"Processing will be skipped for the "
                    f"'{Path(order_tr_fidessa_eod_input.file_uri).name}' file "
                    f"as there are no rows left after pre processing.",
                ],
                models=[RTS22Transaction],
            )
        else:
            # END PRE-PROCESSING #

            # BEGIN BUSINESS LOGIC #

            orders_df = self.run_order_logic(
                input_df=input_df,
                order_tr_fidessa_eod_input=order_tr_fidessa_eod_input,
                app_metrics_path=app_metrics_path,
                aries_task_input=aries_task_input,
                audit_path=audit_path,
                cloud_provider=cloud_provider,
                es_client_srp=es_client_srp,
                es_client_tenant=es_client_tenant,
                streamed=streamed,
                tenant=tenant,
                tenant_bucket_with_cloud_prefix=tenant_bucket_with_cloud_prefix,
            )

            # get only the rows that need to create rts22_transaction records
            rts22_input_df = run_get_rows_by_condition(
                source_frame=input_df,  # type: ignore[arg-type]
                params=GetRowsByConditionParams(
                    query=f"`{AuxiliarColumns.MATCH_TYPE.field}`.astype('str').isin({MatchType.RTS22_TYPES})"
                ),
            )

            if not rts22_input_df.empty:
                (
                    rts22_transaction_df,
                    quarantined_rts22_transaction_df,
                    sink_record_audit_rts22_df,
                ) = self.run_rts22_transaction_logic(
                    rts22_input_df=rts22_input_df,
                    order_tr_fidessa_eod_input=order_tr_fidessa_eod_input,
                    app_metrics_path=app_metrics_path,
                    aries_task_input=aries_task_input,
                    audit_path=audit_path,
                    cloud_provider=cloud_provider,
                    es_client_srp=es_client_srp,
                    es_client_tenant=es_client_tenant,
                    streamed=streamed,
                    tenant=tenant,
                    tenant_bucket_with_cloud_prefix=tenant_bucket_with_cloud_prefix,
                )

        if orders_df.empty:
            orders_ndjson_path = None
        else:
            _audit_fallbacks(
                df=orders_df.frame(),  # type: ignore[operator]
                col_name=INSTRUMENT_PATH,
                fallback_key="isCreatedThroughFallback",
                status_message=StandardAuditMessages.INSTRUMENT_THROUGH_FALLBACK,
                meta_model=Order,
                models=[Order],
                streamed=streamed,
                cloud_provider=cloud_provider,
                app_metrics_path=app_metrics_path,
                audit_path=audit_path,
            )

            orders_ndjson_path = create_ndjson_path(
                tenant_bucket=tenant_bucket_with_cloud_prefix,
                aries_task_input=aries_task_input,
                model=MetaModel.ORDER,
            )

            run_write_ndjson(
                source_serializer_result=orders_df,  # type: ignore[arg-type]
                output_filepath=orders_ndjson_path,
                audit_output=True,
                app_metrics_path=app_metrics_path,
                audit_path=audit_path,
            )

        ## Currently there's never any synthetic new in this feed, leaving implementation
        ## here in case it's needed in the future
        # if synthetic_newo_df.empty:
        #     synthetic_newo_ndjson_path = None
        # else:
        #     # Audit each synthetic NEWO and populate the app metrics synthetic newo count
        #     run_auditor_and_metrics_producer(
        #         source_frame=synthetic_newo_df,
        #         params=AuditorAndMetricsProducerParams(
        #             record_level_audits=[
        #                 RecordLevelAudit(
        #                     query="index==index",
        #                     status_message=StatusDescriptionsOfSyntheticRecords.IS_SYNTHETIC_NEWO,
        #                     meta_model=Order,
        #                 )
        #             ],
        #             metrics=[
        #                 Metrics(
        #                     query="index==index",
        #                     field=OrderAppMetricsEnum.OUTPUT_SYNTHETIC_NEWOS_COUNT,
        #                 )
        #             ],
        #             models=[Order],
        #         ),
        #         streamed=streamed,
        #         cloud_provider=cloud_provider,
        #         app_metrics_path=app_metrics_path,
        #         audit_path=audit_path,
        #     )
        #
        #     # Create the appropriate path where the ndjson result is to be uploaded
        #     _audit_fallbacks(
        #         df=synthetic_newo_df.frame(),  # type: ignore[operator]
        #         col_name=INSTRUMENT_PATH,
        #         fallback_key="isCreatedThroughFallback",
        #         status_message=StandardAuditMessages.INSTRUMENT_THROUGH_FALLBACK,
        #         meta_model=Order,
        #         models=[Order],
        #         streamed=streamed,
        #         app_metrics_path=app_metrics_path,
        #         audit_path=audit_path,
        #         cloud_provider=cloud_provider,
        #     )
        #
        #     synthetic_newo_ndjson_path = create_ndjson_path(
        #         tenant_bucket=tenant_bucket_with_cloud_prefix,
        #         aries_task_input=aries_task_input,
        #         model=MetaModel.ORDER,
        #         suffix="synthetic",
        #     )
        #
        #     # Write the transformed_df data frame into a ndjson file to the generated ndjson path
        #     run_write_ndjson(
        #         source_serializer_result=synthetic_newo_df,  # type: ignore[arg-type]
        #         output_filepath=synthetic_newo_ndjson_path,
        #         audit_output=True,
        #         app_metrics_path=app_metrics_path,
        #         audit_path=audit_path,
        #     )

        ### define outputs ###
        order_output = add_nested_params(
            file_uri=orders_ndjson_path,
            es_action=EsActionEnum.INDEX,
            data_model=Order.get_reference().get_qualified_reference(),
            ignore_empty_file_uri=True,
        )

        ## Currently there's never any synthetic new in this feed, leaving implementation
        ## here in case it's needed in the future
        # synthetic_newo_output = add_nested_params(
        #     file_uri=synthetic_newo_ndjson_path,
        #     es_action=EsActionEnum.CREATE,
        #     data_model=Order.get_reference().get_qualified_reference(),
        #     ignore_empty_file_uri=True
        # )

        rts22_transaction_output = create_path_and_upload_model_results(
            final_transformed_df=rts22_transaction_df,
            aries_task_input=aries_task_input,
            tenant_bucket=tenant_bucket_with_cloud_prefix,
            model=RTS22Transaction,
            es_action=EsActionEnum.CREATE,
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        quarantined_rts22_transaction_output = create_path_and_upload_model_results(
            final_transformed_df=quarantined_rts22_transaction_df,
            aries_task_input=aries_task_input,
            tenant_bucket=tenant_bucket_with_cloud_prefix,
            model=QuarantinedRTS22Transaction,
            es_action=EsActionEnum.INDEX,
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        sink_record_audit_output = create_path_and_upload_model_results(
            final_transformed_df=sink_record_audit_rts22_df,
            aries_task_input=aries_task_input,
            tenant_bucket=tenant_bucket_with_cloud_prefix,
            model=SinkRecordAudit,
            es_action=EsActionEnum.INDEX,
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        shutil.rmtree(tmp_storage)
        finish_flow(
            result_path=result_path,
            result_data={
                ## Currently there's never any synthetic new in this feed, leaving implementation
                ## here in case it's needed in the future
                # "synthetic_newo": synthetic_newo_output,
                "orders": order_output,
                MetaModel.RTS22_TRANSACTION: rts22_transaction_output,
                MetaModel.QUARANTINED_RTS22_TRANSACTION: quarantined_rts22_transaction_output,
                MetaModel.SINK_RECORD_AUDIT: sink_record_audit_output,
            },
        )

    @staticmethod
    def run_order_logic(
        input_df: SerializerResult,
        order_tr_fidessa_eod_input: OrderTrFidessaEODAriesTaskInput,
        app_metrics_path: str,
        aries_task_input: AriesTaskInput,
        audit_path: str,
        cloud_provider: CloudProviderEnum,
        es_client_srp: ElasticsearchRepository,
        es_client_tenant: ElasticsearchRepository,
        streamed: bool,
        tenant: str,
        tenant_bucket_with_cloud_prefix: str,
    ) -> pd.DataFrame:
        order_mappings_df = run_get_primary_transformations(
            source_frame=input_df,
            flow=OrderWorkflowNames.ORDER_TR_FIDESSA_EOD_ORDER,
            tenant=tenant,
            es_client=es_client_tenant,
            realm=tenant_bucket_with_cloud_prefix,
            source_file_uri=order_tr_fidessa_eod_input.source_file_uri,
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # Populate the `&parent` field for OrderStates
        meta_parent_df = run_assign_meta_parent(
            source_frame=order_mappings_df,
            params=MetaParentParams(
                parent_model_attribute=add_prefix(
                    prefix=ModelPrefix.ORDER, attribute=OrderColumns.META_MODEL
                ),
                parent_attributes_prefix=ModelPrefix.ORDER_DOT,
                target_attribute=add_prefix(prefix=ModelPrefix.ORDER_STATE, attribute=PARENT),
            ),
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # Link identifiers built in InstrumentIdentifier from input instrument
        # data with SRP and tenant instrument data
        link_instrument_df = run_link_instrument(
            source_frame=order_mappings_df,
            params=LinkInstrumentParams(
                identifiers_path=OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT,
                currency_attribute=OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY,
                venue_attribute=OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE,
            ),
            tenant=aries_task_input.workflow.tenant,
            es_client_srp=es_client_srp,
            es_client_tenant=es_client_tenant,
        )

        # Link identifiers built in BlotterPartyIdentifiers from
        # input party data with tenant MyMarket data
        link_parties_df = run_link_parties(
            tenant=tenant,
            source_frame=order_mappings_df,
            params=LinkPartiesParams(identifiers_path=OrderColumns.MARKET_IDENTIFIERS_PARTIES),
            es_client=es_client_tenant,
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        party_fallback_input = run_frame_concatenator(
            transformed_df=order_mappings_df,
            link_parties_df=link_parties_df,
            params=FrameConcatenatorParams(orient=OrientEnum.horizontal),
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # Create Party records embedded in the Orders for records
        # where LinkParties did not produce any hits
        party_fallback_df = run_party_fallback_with_lei_lookup(
            source_frame=party_fallback_input,
            params=PartyFallbackParams(),
        )

        instrument_fallback_input = run_frame_concatenator(
            transformed_df=order_mappings_df,
            link_instrument_df=link_instrument_df,
            params=FrameConcatenatorParams(orient=OrientEnum.horizontal),
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # Create Instrument records embedded in the Orders for records
        # where LinkInstrument did not produce any hits
        instrument_fallback_df = run_instrument_fallback(
            source_frame=instrument_fallback_input,
            params=InstrumentFallbackParams(
                market_instrument_identifiers_attribute=OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT,
                instrument_fields_map=[
                    # ISIN and Currency are populated directly from
                    # the respective instrument identifier columns in
                    # INSTRUMENT_IDENTIFIER_TO_INSTRUMENT_FIELD_MAP
                    FallbackInstrumentAttributes(
                        source_field=TempColumns.INSTRUMENT_IS_CREATED_THROUGH_FALLBACK,
                        target_field=InstrumentFields.IS_CREATED_THROUGH_FALLBACK,
                    ),
                ],
                str_to_bool_dict={"true": True, "false": False},
            ),
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # Concat all relevant DataFrames and discard temporary
        # columns that must not be part of the final result
        aux_df = run_frame_concatenator(
            order_mappings_df=order_mappings_df,
            meta_parent_df=meta_parent_df,
            party_fallback_df=party_fallback_df,
            instrument_fallback_df=instrument_fallback_df,
            params=FrameConcatenatorParams(
                orient=OrientEnum.horizontal,
                drop_columns=[
                    OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT,
                    OrderColumns.MARKET_IDENTIFIERS_PARTIES,
                    "expiry_date_attribute",
                    "instrument_classification_attribute",
                    "notional_currency_2_attribute",
                    "option_strike_price_attribute",
                    "option_type_attribute",
                    "underlying_symbol_attribute",
                    "underlying_symbol_expiry_code_attribute",
                    "currency_attribute",
                    "venue_attribute",
                    TempColumns.INSTRUMENT_IS_CREATED_THROUGH_FALLBACK,
                ]
                + list(PARTY_FILE_ID_FALLBACK_COL_MAPPING.values()),
            ),
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # Split the columns pertaining to Orders to a separate DataFrame
        order_records_df = run_frame_splitter(
            source_frame=aux_df,
            params=FrameSplitterParams(
                except_prefix=ModelPrefix.ORDER_STATE_DOT, strip_prefix=True
            ),
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # Strip the Order prefix
        parsed_order_records_df = run_frame_column_manipulator(
            source_frame=order_records_df,
            params=FrameColumnManipulatorParams(action=Action.strip, prefix=ModelPrefix.ORDER_DOT),
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # Split the columns pertaining to OrderStates to a separate DataFrame
        order_state_records_df = run_frame_splitter(
            source_frame=aux_df,
            params=FrameSplitterParams(except_prefix=ModelPrefix.ORDER_DOT, strip_prefix=True),
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # Strip the OrderState prefix
        parsed_order_state_records_df = run_frame_column_manipulator(
            source_frame=order_state_records_df,
            params=FrameColumnManipulatorParams(
                action=Action.strip, prefix=ModelPrefix.ORDER_STATE_DOT
            ),
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # Concat a final DataFrame that contains all Order +
        # OrderState records without any temporary columns/prefixes
        orders_and_order_states_df = run_frame_concatenator(
            parsed_order_records_df=parsed_order_records_df,
            parsed_order_state_records_df=parsed_order_state_records_df,
            params=FrameConcatenatorParams(
                orient=OrientEnum.vertical, reset_index=True, drop_index=True
            ),
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # Exclude all records that do not have a value in the "executionDetails.orderStatus" field
        # Such records are either missing from the input data column `ORDERSTATUS` or they have
        # an invalid Order Status which is not part of the map defined in the PrimaryTransformations
        filtered_orders_and_order_states_df = run_get_rows_by_condition(
            source_frame=orders_and_order_states_df,
            params=GetRowsByConditionParams(query="`executionDetails.orderStatus`.notnull()"),
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # Remove duplicate NEWOs from dataset (i.e. synthetic NEWOs that were created unnecessarily)
        deduplicated_data_df = run_remove_duplicate_newo(
            source_frame=filtered_orders_and_order_states_df,
            params=RemoveDuplicateNewoParams(
                newo_in_file_col=OrderTempColumns.NEWO_IN_FILE,
                drop_newo_in_file_col=True,
                query_elastic_for_existing_newo=True,
            ),
            tenant=aries_task_input.workflow.tenant,
            es_client=es_client_tenant,
            streamed=streamed,
            cloud_provider=cloud_provider,
            audit_path=audit_path,
            app_metrics_path=app_metrics_path,
        )

        best_execution_df = run_best_execution(
            source_frame=deduplicated_data_df,
            es_client=es_client_tenant,
            streamed=streamed,
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # Note that we are not discarding the `&parent` column here
        # this can be propagated to the ApplyMeta Conductor Task, which will reuse it
        final_result_df = run_frame_concatenator(
            best_execution_df=best_execution_df,
            deduplicated_data_df=deduplicated_data_df,
            params=FrameConcatenatorParams(
                orient=OrientEnum.horizontal, drop_columns=[OrderColumns.META_MODEL]
            ),
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # Audit missing instruments in lookup file
        run_auditor_and_metrics_producer(
            source_frame=final_result_df,
            params=AuditorAndMetricsProducerParams(
                record_level_audits=[
                    RecordLevelAudit(
                        query="`isin_attribute`.isnull()",
                        status_message="Unable to find Instrument match in INST_LOOKUP_EQ file",
                        meta_model=Order,
                    )
                ],
                models=[Order],
            ),
            streamed=streamed,
            cloud_provider=cloud_provider,
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        final_result_df = run_frame_concatenator(
            meta_transcript_df=final_result_df,
            params=FrameConcatenatorParams(
                orient=OrientEnum.horizontal,
                drop_columns=["isin_attribute"],
            ),
        )

        ## Currently there's never any synthetic new in this feed, leaving implementation
        ## here in case it's needed in the future
        # # CREATE SYNTHETIC NEWOs
        # synthetic_newo_df = run_get_rows_by_condition(
        #     source_frame=final_result_df,
        #     params=GetRowsByConditionParams(query=f"`{OrderColumns.IS_SYNTHETIC}` == True"),
        # )
        # # INDEX Orders
        # orders_df = run_get_rows_by_condition(
        #     source_frame=final_result_df,
        #     params=GetRowsByConditionParams(query=f"`{OrderColumns.IS_SYNTHETIC}` != True"),
        # )

        return final_result_df  # type: ignore[no-any-return]

    @staticmethod
    def run_rts22_transaction_logic(
        rts22_input_df: SerializerResult,
        order_tr_fidessa_eod_input: OrderTrFidessaEODAriesTaskInput,
        app_metrics_path: str,
        aries_task_input: AriesTaskInput,
        audit_path: str,
        cloud_provider: CloudProviderEnum,
        es_client_srp: ElasticsearchRepository,
        es_client_tenant: ElasticsearchRepository,
        streamed: bool,
        tenant: str,
        tenant_bucket_with_cloud_prefix: str,
    ) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
        rts22_transaction_mappings_df = run_get_primary_transformations(
            source_frame=rts22_input_df,
            flow=RTS22TransactionWorkflowNames.ORDER_TR_FIDESSA_EOD_TR,
            tenant=tenant,
            es_client=es_client_tenant,
            realm=tenant_bucket_with_cloud_prefix,
            source_file_uri=order_tr_fidessa_eod_input.source_file_uri,
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # Link identifiers built in InstrumentIdentifier from input instrument
        # data with SRP and tenant instrument data
        link_instrument_df = run_link_instrument(
            source_frame=rts22_transaction_mappings_df,
            params=LinkInstrumentParams(
                identifiers_path=RTS22TransactionColumns.MARKET_IDENTIFIERS_INSTRUMENT,
                currency_attribute=RTS22TransactionColumns.TRANSACTION_DETAILS_PRICE_CURRENCY,
                venue_attribute=RTS22TransactionColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE,
            ),
            tenant=tenant,
            es_client_srp=es_client_srp,
            es_client_tenant=es_client_tenant,
        )

        instrument_fallback_input = run_frame_concatenator(
            transformed_df=rts22_transaction_mappings_df,
            link_instrument_df=link_instrument_df,
            params=FrameConcatenatorParams(orient=OrientEnum.horizontal),
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # Create Instrument records embedded in the Orders for records
        # where LinkInstrument did not produce any hits
        instrument_fallback_df = run_instrument_fallback(
            source_frame=instrument_fallback_input,
            params=InstrumentFallbackParams(
                market_instrument_identifiers_attribute=RTS22TransactionColumns.MARKET_IDENTIFIERS_INSTRUMENT,
                instrument_fields_map=[
                    # ISIN and Currency are populated directly from
                    # the respective instrument identifier columns in
                    # INSTRUMENT_IDENTIFIER_TO_INSTRUMENT_FIELD_MAP
                    FallbackInstrumentAttributes(
                        source_field=TempColumns.INSTRUMENT_IS_CREATED_THROUGH_FALLBACK,
                        target_field=InstrumentFields.IS_CREATED_THROUGH_FALLBACK,
                    ),
                ],
                str_to_bool_dict={
                    "true": True,
                    "false": False,
                },
            ),
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # Link identifiers built in BlotterPartyIdentifiers from
        # input party data with tenant MyMarket data
        link_parties_df = run_link_parties(
            tenant=aries_task_input.workflow.tenant,
            source_frame=rts22_transaction_mappings_df,
            params=LinkPartiesParams(
                identifiers_path=RTS22TransactionColumns.MARKET_IDENTIFIERS_PARTIES,
                add_investment_firm_covered_directive=True,
            ),
            es_client=es_client_tenant,
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        eligibility_assessor_input = run_frame_concatenator(
            transformed_df=rts22_transaction_mappings_df,
            instrument_fallback_df=instrument_fallback_df,
            params=FrameConcatenatorParams(orient=OrientEnum.horizontal),
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        eligibility_assessor_df = run_eligibility_assessor(
            source_frame=eligibility_assessor_input,
            tenant=tenant,
            es_client=es_client_tenant,
            srp_client=es_client_srp,
        )

        final_frame = run_frame_concatenator(
            transformed_df=rts22_transaction_mappings_df,
            instrument_fallback_df=instrument_fallback_df,
            eligibility_assessor_df=eligibility_assessor_df,
            link_parties_df=link_parties_df,
            params=FrameConcatenatorParams(
                orient=OrientEnum.horizontal,
                drop_columns=[
                    RTS22TransactionColumns.MARKET_IDENTIFIERS_INSTRUMENT,
                    RTS22TransactionColumns.MARKET_IDENTIFIERS_PARTIES,
                    TempColumns.INSTRUMENT_IS_CREATED_THROUGH_FALLBACK,
                ],
            ),
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # Audit missing instruments in lookup file
        run_auditor_and_metrics_producer(
            source_frame=final_frame,
            params=AuditorAndMetricsProducerParams(
                record_level_audits=[
                    RecordLevelAudit(
                        query="`isin_attribute`.isnull()",
                        status_message="Unable to find Instrument match in INST_LOOKUP_EQ file.",
                        meta_model=RTS22Transaction,
                    )
                ],
                models=[RTS22Transaction],
            ),
            streamed=streamed,
            cloud_provider=cloud_provider,
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        final_frame = run_frame_concatenator(
            meta_transcript_df=final_frame,
            params=FrameConcatenatorParams(
                orient=OrientEnum.horizontal,
                drop_columns=["isin_attribute"],
            ),
        )

        rts22_transaction_df, quarantined_rts22_transaction_df, sink_record_audit_df = (
            run_quarantined_records(
                source_frame=final_frame,
                tenant=tenant,
                model=RTS22Transaction,
                cloud_provider=cloud_provider,
                aries_task_input=aries_task_input,
                es_client=es_client_tenant,
                audit_path=audit_path,
                app_metrics_path=app_metrics_path,
            )
        )

        _audit_fallbacks(
            df=rts22_transaction_df.frame(),  # type: ignore[operator]
            col_name=INSTRUMENT_PATH,
            fallback_key="isCreatedThroughFallback",
            status_message=StandardAuditMessages.INSTRUMENT_THROUGH_FALLBACK,
            meta_model=RTS22Transaction,
            models=[RTS22Transaction],
            streamed=streamed,
            cloud_provider=cloud_provider,
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        return rts22_transaction_df, quarantined_rts22_transaction_df, sink_record_audit_df

    @staticmethod
    def instantiate_srp_es_client(srp_through_master_data: bool):
        if srp_through_master_data:
            raise Exception("Master Data connection is currently not supported for this feed.")

        es_client_srp: ElasticsearchRepository = get_repository_by_cluster_version(
            resource_config=get_srp_es_config()
        )
        return es_client_srp

    def skip_logic(
        self,
        input_df: SerializerResult,
        streamed: bool,
        cloud_provider: CloudProviderEnum,
        app_metrics_path: str,
        audit_path: str,
    ) -> SerializerResult:
        """For some clients, we want to add additional skip logic This is
        implemented in an override."""
        return input_df


def _audit_fallbacks(
    df: pd.DataFrame,
    col_name: str,
    fallback_key: str,
    status_message: str,
    meta_model: Type[SteelEyeSchemaBaseModelES8],
    models: List[Type[SteelEyeSchemaBaseModelES8]],
    streamed: bool,
    cloud_provider: CloudProviderEnum,
    app_metrics_path: str,
    audit_path: str,
):
    instruments_created_through_fallback = (
        df.loc[:, col_name].str.get(fallback_key).fillna(False)  # type: ignore
    )
    if any(instruments_created_through_fallback):
        run_auditor_and_metrics_producer(
            source_frame=df.loc[instruments_created_through_fallback, :],
            params=AuditorAndMetricsProducerParams(
                record_level_audits=[
                    RecordLevelAudit(
                        query="index==index",
                        status_message=status_message,
                        meta_model=meta_model,
                    )
                ],
                metrics=[
                    Metrics(
                        query="index==index",
                        field=OrderAppMetricsEnum.SYNTHETIC_INSTRUMENTS_COUNT,
                    )
                ],
                models=models,
            ),
            streamed=streamed,
            cloud_provider=cloud_provider,
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )


def order_tr_fidessa_eod_flow(
    flow_override_class,
    aries_task_input: AriesTaskInput,
    app_metrics_path: str,
    audit_path: str,
    result_path: str,
):
    flow_override_class().run_flow(
        aries_task_input=aries_task_input,
        app_metrics_path=app_metrics_path,
        audit_path=audit_path,
        result_path=result_path,
    )
