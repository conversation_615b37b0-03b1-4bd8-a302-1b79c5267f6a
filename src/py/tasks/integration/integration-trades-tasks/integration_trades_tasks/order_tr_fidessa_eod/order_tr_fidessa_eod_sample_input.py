import datetime
from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput


def sample_input() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        trace_id="order_tr_fidessa_eod_trace_id",
        name="order_tr_fidessa_eod",
        stack="dev-shared-2",
        tenant="mares8",
        start_timestamp=datetime.datetime.utcnow(),
    )
    input_param = IOParamFieldSet(
        params=dict(
            # file_uri=(  # full_match and no_order_match file
            #     "s3://mares8.dev.steeleye.co/aries/ingest/order_blotter/2024/12/02/trace_id:123/"
            #     "file_splitter_by_criteria/FIDESSA_EOD_MERGED_20241029_00.csv"
            # ),
            file_uri=(  # no_execution_match file
                "s3://mares8.dev.steeleye.co/aries/ingest/order_tr_fidessa_eod/2024/10/29/hf89234g5f9378rf9/file_splitter_by_criteria/FIDESSA_EOD_MERGED_20250108_22.psv"
            ),
            source_file_uri=(
                "s3://mares8.dev.steeleye.co/aries/ingress/nonstreamed/evented/"
                "order_tr_fidessa_eod/EXECUTION_LIST.20241029.psv"
                # "s3://mares8.dev.steeleye.co/aries/ingress/nonstreamed/evented/"
                # "order_tr_fidessa_eod/ORDER.20241029.psv"
            ),
        )
    )
    task = TaskFieldSet(name="order_tr_fidessa_eod", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)
