# ruff: noqa: E501
# mypy: disable-error-code="no-any-return, return-value, dict-item, arg-type, var-annotated, return, assignment, override"
import json
import os
import requests
import sys
from addict import Dict
from datetime import datetime
from integration_trades_tasks.trade_sink.fix.fix_parser import TradeSinkFixParser
from integration_trades_tasks.trade_sink.fix.fix_util import fix_getter
from integration_trades_tasks.trade_sink.utils import iterators, static
from integration_trades_tasks.trade_sink.utils import trade_util as utl
from integration_trades_tasks.trade_sink.utils.base_classes.trade_handler import (
    AbstractTradeHandler,
)
from integration_trades_tasks.trade_sink.utils.convention import log
from integration_trades_tasks.trade_sink.utils.data_util import date, dtm, is_empty, process_dtm
from integration_trades_tasks.trade_sink.utils.dict_util import stripper
from integration_trades_tasks.trade_sink.utils.exception import (
    ExternalRequiredError,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    TransformExternalError,
)
from pathlib import Path
from typing import Iterable, List, Tuple

DTM_FMT = static.YEAR_FIRST
CHECKED_TENANTS = ["ena", "ibp"]
NEWO_SET = set()


PACKAGE_PATH = Path(sys.modules["integration_trades_tasks.trade_sink"].__file__).parent
RESOURCE_PATH = Path(PACKAGE_PATH, "resources")
REF_FILE = Path(RESOURCE_PATH, "bloomberg-fix_reference.json")
EXCHANGE_MAP = json.load(REF_FILE.open())


class TradeHandler(AbstractTradeHandler):
    def __init__(self, content, event, firm, data_source):
        super(TradeHandler, self).__init__(content, event, firm, data_source)
        self.overwrite = False

    def iterator(self, **kwargs) -> Iterable[str]:
        for fix in iterators.batch_iterator(self.event, self.content):
            for record in iterators.fix_iterator(fix):
                yield record

    def transform(self, record: str) -> Tuple[List[dict], List[dict]]:
        parser = TradeSinkFixParser()
        parser.parse(record)
        fix_msg = parser.export()
        fget = fix_getter(fix_msg)
        if fget(35, default="").upper() == "B":
            raise SkipRecord("Skipped: Invalid message type: 35=B")

        self.verify(fget)
        order_states = self.get_order_states(fget)
        ex = [self.ex_report(fget, s) for s in order_states]
        return ex, []

    def verify(self, fget):
        realm = self.event.get("bucket_name", "").split(".")[0].lower()
        tenant = next(filter(lambda x: x in realm, CHECKED_TENANTS), None)
        fix_feed = fget(56, default="").lower()
        if tenant and tenant not in fix_feed:
            raise TransformExternalError(f"Feed {fix_feed} invalid for {realm}")

    def tc_report(self, fget) -> Dict:
        px_converter = utl.major_currency_price_converter(fget, 15)

        rpt = Dict()
        rpt.SettlementAmount = fget(119, typ="abs", default=0.0)
        rpt.SettlementAmountCurrency = fget(15) if not is_empty(fget(119)) else None
        rpt.TradeID = get_trade_id(fget)
        rpt.TradeLinkID = fget(961, default=fget(820))
        rpt.Text = get_text(fget)
        rpt.AggressorIndicator = get_agg_ind(fget)
        rpt.EffectiveTime = fget(432)
        rpt.Price2 = px_converter(44)
        rpt.StopPx = px_converter(99)
        rpt.Instrument.StrikePrice = px_converter(202)
        rpt.LastPx = px_converter(31)
        rpt.BidPx = px_converter(132)
        rpt.AskPx = px_converter(133)
        rpt.Currency = utl.to_major_currency(fget(15))
        rpt.PriceType = fget(423) or "MONE"
        rpt.CumQty = fget(14, typ="abs", default=0.0)
        rpt.MinQty = fget(110, typ="abs", default=0.0)
        rpt.MaxFloor = fget(111, typ="abs", default=0.0)
        rpt.OrderQtyData.OrderQty = fget(38, typ="abs", default=0.0)
        rpt.Quantity = fget(53, typ="abs", default=0.0)
        rpt.LastQty = fget(32, typ="abs", default=0.0)
        rpt.Instrument.ContractMultiplier = fget(231)
        rpt.PositionEffect = fget(70)
        rpt.Instrument.SecurityExchange = get_security_exchange(fget)
        rpt.LastMkt = fget(1300, default="XOFF")
        rpt.TrdCapRptSideGrp = get_trd_side_grp(fget)
        rpt.PaymentGrp = get_payment_grp(fget)
        rpt.RegulatoryTradeIDGrp = get_regulatory_trade_id_grp(fget)
        rpt.TrdRegTimestamps = self.fronos(fget)
        rpt.SteelEyeParties = self.patty(fget)
        rpt.Instrument.SecurityID = self.icarus(fget)
        rpt.QtyType = "MONE" if fget(854) == "0" or fget(22) == "6" else "UNIT"
        if rpt.QtyType == "MONE":
            rpt.Instrument.PriceQuoteCurrency = fget(120) if fget(22) == "6" else fget(15)

        # new fields
        rpt.CommissionAmount = fget(12, typ="abs", default=0.0)
        rpt.CommissionAmountCurrency = fget(15)
        rpt.CommissionAmountType = fget(13)
        rpt.BasketID = fget(5419)
        rpt.PriceAverage = px_converter(6)
        rpt.Flags.overwrite = self.overwrite
        return rpt

    def ex_report(self, fget, ord_status: str) -> Dict:
        if ord_status == static.ORD_STATUS_MAP.NEWO:
            NEWO_SET.add(get_order_id(fget))
        rpt = self.tc_report(fget)
        rpt.OrdStatus = ord_status
        rpt.OrdType = fget(40)
        rpt.LeavesQty = fget(151, typ="abs", default=0.0)
        rpt.TimeInForceRules = [fget(432)]
        rpt.OrderID = get_order_id(fget)
        rpt.SecondaryOrderID = fget(11, default=fget(198))
        rpt.ClOrdLinkID = fget(583)
        utl.update_for_order_status(rpt, ord_status)
        return rpt

    def patty(self, fget) -> dict:
        buyers, sellers = list(), list()
        party_label = "ID"
        party_ids = get_party_ids(fget)
        side = get_side(fget)

        party = utl.party(party_ids.firm_id, party_label, "27")
        counterparty = utl.party(party_ids.counterparty_id, party_label, "27")
        if side in static.BUY:
            buyers.append(party)
            sellers.append(counterparty)
        elif side in static.SELL:
            sellers.append(party)
            buyers.append(counterparty)

        if get_order_cap(fget) in ("DEAL", "G", "I", "P", "R"):
            dec_maker = utl.party(party_ids.decision_maker_id, party_label, "122")
            exec_in_firm = utl.party(party_ids.exec_in_firm_id, party_label, "12")
        else:
            exec_in_firm = utl.party("nore", "clnt", "12")
            dec_maker = None

        return stripper(
            dict(
                Client=utl.party(party_ids.client_id, party_label, "3"),
                Buyer=buyers,
                Seller=sellers,
                ExecutingFirm=utl.party(party_ids.firm_id, party_label, "1"),
                ExecutionWithinFirm=exec_in_firm,
                Trader=utl.party(party_ids.trader_id, party_label, "12"),
                DecisionMakerWithinFirm=dec_maker,
                Counterparty=counterparty,
            )
        )

    def icarus(self, fget):
        val = None
        exch_mic = "XXXX"
        ff_167 = fget(167, default="").upper()
        ff_48 = fget(48, default=fget(55, default="")).replace("/", "")

        if fget(22) == "4":
            val = [f"{fget(48)}{fget(15)}{fget(30)}", fget(48)]
        elif not is_empty(fget(455)) and fget(456) == "4":
            val = [fget(455), fget(48)]
        elif ff_167 == "CDS":
            val = credit_default_swap(fget)
        elif ff_167 in ("FXFWD", "FXNDF"):
            val = "".join([exch_mic, ff_48, "FXFWD", date(fget(64, default=""), DTM_FMT)])
        elif ff_167 == "FXSPOT":
            val = "".join([exch_mic, ff_48, "FXSPOT"])
        elif ff_167 == "FUT":
            val = [
                "".join(
                    [
                        fget(30) if fget(30) not in ("XOFF", None) else exch_mic,
                        strip_if_ends_alnum(fget(48)),
                        "FF",
                        "-".join((date(fget(200), DTM_FMT) or "").split("-")[:2]),
                        " 00:00:00",
                    ]
                )
            ]
        elif ff_167 == "OPT" and fget(22) == "6":
            val = "".join(
                [
                    exch_mic,
                    ff_48,
                    "O",
                    "P" if fget(201) == "1" else "C",
                    date(fget(541, default=""), DTM_FMT),
                ]
            )
        elif ff_167 == "OPT":
            val = [
                "".join(
                    [
                        fget(30) if fget(30) not in ("XOFF", None) else exch_mic,
                        strip_if_ends_alnum(fget(48)),
                        "O",
                        "P" if fget(201) == "O" else "C",
                        "-".join((date(fget(200), DTM_FMT) or "").split("-")[:2]),
                        " 00:00:00",
                        f"{'{:0.8f}'.format(fget(202, typ='abs'))}",
                    ]
                )
            ]
        elif ff_167 in ("FXSWAP", "FXNDS"):
            val = fx_swap(fget, ff_48)
        elif fget(55):
            val = fget(55)
        elif fget(48):
            val = fget(48)

        open_figi_val = self.open_figi(fget)
        if not is_empty(open_figi_val):
            val = [val] if not isinstance(val, list) else val
            val.append(open_figi_val)

        if not is_empty(val):
            return val
        else:
            raise ExternalRequiredError(
                [
                    f"Security Type: {ff_167}",
                    f"ID Source: {fget(22)}",
                    f"Symbol: {fget(55)}",
                    f"Security ID: {fget(48)}",
                    f"Value: {val}",
                ]
            )

    @staticmethod
    def open_figi(fget):
        val = None
        ff_167 = fget(167)

        if fget(460) in ("1", "3", "6"):
            if fget(22) == "4":
                val = fget(48)
            elif fget(456) == "4":
                val = fget(455)

        if ff_167 == "FUT":
            val = open_figi_id(
                idType="TICKER",
                idValue=fget(48),
                currency=fget(15),
                securityType2="Future",
            )
        elif ff_167 == "OPT":
            val = open_figi_option_query(fget)
        elif ff_167 == "NONE" and "INDEX" in fget(55, default="").upper():
            val = open_figi_id(idType="ID_EXCH_SYMBOL", idValue=fget(48), securityType2="Index")

        return val

    def fronos(self, fget) -> List[dict]:
        trading_datetime = utl.next_existing(fget(60), fget(75), fget(52))
        order_submitted = (
            fget(769) if fget(770) in ("10", "4") and not is_empty(fget(769)) else trading_datetime
        )
        utl.assert_external_required(
            orderSubmitted=order_submitted, TradingDateTime=trading_datetime
        )
        return [
            utl.kronos_block(date(order_submitted, DTM_FMT), "98"),
            utl.kronos_block(date(fget(64), DTM_FMT), "17"),
            utl.kronos_block(dtm(order_submitted, DTM_FMT), "4"),
            utl.kronos_block(dtm(trading_datetime, DTM_FMT), "3"),
            utl.kronos_block(dtm(trading_datetime, DTM_FMT), "1"),
        ]

    def get_order_states(self, fget) -> List[str]:
        self.overwrite = False

        order_state = fget(150)
        if is_empty(order_state) or order_state in ("F", "3", "6"):
            order_state = {
                "3": static.ORD_STATUS_MAP.EXPI,
                "6": static.ORD_STATUS_MAP.CHME,
                "D": static.ORD_STATUS_MAP.CHME,
            }.get(fget(39), fget(39))

        if is_empty(order_state):
            params = {"150": fget(150), "39": fget(39), "Order Status": order_state}
            raise ExternalRequiredError(
                [utl.format_additional_text(k, v) for k, v in params.items()]
            )
        else:
            states = [order_state]

        if order_state == static.ORD_STATUS_MAP.NEWO:
            self.overwrite = True
        elif get_order_id(fget) not in NEWO_SET:
            states.append(static.ORD_STATUS_MAP.NEWO)

        return states


def get_trd_side_grp(fget) -> List[dict]:
    utl.assert_external_required(Side=get_side(fget))

    attr_grp = Dict()
    attr_grp.OrderAttributeType = "3"
    attr_grp.OrderAttributeValue = "N"

    grp = Dict()
    grp.Side = get_side(fget)
    grp.NetMoney = fget(118, typ="abs", default=0.0)
    grp.TradeReportOrderDetail.OrderCapacity = get_order_cap(fget)
    grp.TradeReportOrderDetail.OrderAttributeGrp = [attr_grp]
    return [grp]


def get_payment_grp(fget) -> List[dict]:
    if fget(40213) == "1" and fget(167, default="").upper() == "CDS":
        return [
            Dict(
                PaymentAmount=fget(40217, typ="abs", default=0.0),
                PaymentPaySide=get_side(fget),
                PaymentCurrency=utl.to_major_currency(fget(40216)),
                PaymentType="1",
            )
        ]


def get_regulatory_trade_id_grp(fget) -> List[dict]:
    return [dict(RegulatoryTradeID=fget(1903))]


def get_trade_id(fget) -> str:
    return "|".join([get_order_id(fget), fget(17, default="")])[:52]


def get_order_id(fget) -> str:
    return fget(19, default=fget(37, default=""))


def get_order_cap(fget) -> str:
    if not is_empty(fget(528)):
        return static.ORDER_CAPACITY_MAP.get(fget(528), static.OrderCapacity.AOTC)

    capacity_map = {
        "1": static.OrderCapacity.AOTC,
        "2": static.OrderCapacity.MTCH,
        "3": static.OrderCapacity.DEAL,
        "4": static.OrderCapacity.DEAL,
        "5": static.OrderCapacity.DEAL,
    }
    return capacity_map.get(fget(29), static.OrderCapacity.AOTC)


def get_text(fget) -> str:
    text_map = {
        "Coupon Rate": 223,
        "Yield": 236,
        "Notes": 9613,
        "Rejected Reason": 103,
        "Sender Comp ID": 49,
        "Target Comp ID": 56,
    }
    if fget(452) == "13":
        text_map.update({"Execution System": 448})

    text = utl.get_text_from_map(fget, **text_map)
    if not is_empty(fget(828)):
        trade_type_map = {
            "0": "RegularTrade",
            "2": "EFP",
            "7": "BunchedTrade",
            "47": "FinancingTransaction",
            "58": "BlockSwapTrade",
            "65": "PackageTrade",
            "2000": "RollTrade",
            "2001": "CurveTrade",
            "2002": "ProductSwitchTrade",
            "2003": "Termination",
        }
        text += f", Trade Type - {trade_type_map.get(fget(828))}"
    if not is_empty(fget(829)):
        trade_sub_type_map = {
            "37": "CrossedTrade",
            "2000": "OutrightSwap",
            "2001": "InputInputSwap",
            "2002": "ParParSwap",
            "2003": "CurrencyCurrencySwap",
            "2004": "RiskNeutralSwap",
            "2005": "SwapVersusBond",
            "2006": "TBARoundRobin",
            "2007": "NonDeltaNeutralSwitch",
            "2008": "FourToOneSwitchRatio",
        }
        text += f", Trade Sub Type - {trade_sub_type_map.get(fget(829))}"
    return text


def get_agg_ind(fget) -> str:
    if fget(851) == "1" or fget(1057) == "N":
        return "N"
    elif fget(851) == "2" or fget(1057) == "Y":
        return "Y"


def credit_default_swap(fget) -> str:
    if fget(1939) == "1":
        return f"XXXX|{fget(311)}|CDS|{date(fget(64, default=''), DTM_FMT)}"
    elif fget(1939) == "5":
        return "|".join(
            [
                "XXXX",
                fget(311).replace(" ", ""),
                "CDS",
                fget(63),
                fget(1957),
                fget(1958),
                date(fget(64, default=""), DTM_FMT),
            ]
        )


def fx_swap(fget, ff_48) -> str:
    inst_1 = f"XXXX{ff_48}FXSWAP"
    inst_2 = f"XXXX{fget(600)}{fget(555)}FXFWD{date(fget(611, default=''), DTM_FMT)}"
    inst_3 = "".join(
        ["XXXX", fget(600, pos=2), "FXFWD", date(fget(611, pos=2, default=""), DTM_FMT)]
    )
    return "|".join([inst_1, inst_2, inst_3])


def get_party_ids(fget) -> Dict:
    firm_id = fget(56)
    trader_id = fget(50)
    client_id = fget(1)
    counterparty_id = fget(76)
    decision_maker_id = exec_in_firm_id = None

    ff_452 = fget(452, all_vals=True)
    ff_448 = fget(448, all_vals=True)
    for i, v in enumerate(ff_452):
        if v == "13":
            firm_id = ff_448[i]
        elif v == "11":
            trader_id = ff_448[i]
        elif v == "3":
            client_id = ff_448[i]
        elif v == "17":
            counterparty_id = ff_448[i]
        elif v == "1" and ff_448[i] == "FCS" and firm_id == "MAP_FCSC_PROD":
            counterparty_id = fget(9613)
        elif v == "122":
            decision_maker_id = ff_448[i]
        elif v == "12":
            exec_in_firm_id = ff_448[i]
        elif v == "1" and is_empty(counterparty_id):
            counterparty_id = ff_448[i]

    trader_fallback = utl.fallback(trader_id)
    decision_maker_id = trader_fallback(decision_maker_id)
    exec_in_firm_id = trader_fallback(exec_in_firm_id)

    return Dict(
        firm_id=firm_id,
        trader_id=trader_id,
        client_id=client_id,
        counterparty_id=counterparty_id,
        decision_maker_id=decision_maker_id,
        exec_in_firm_id=exec_in_firm_id,
    )


def get_security_exchange(fget) -> str:
    return next(
        (
            value
            for value, condition in [
                (fget(207), len(fget(207, default="")) == 4),
                (fget(30), len(fget(30, default="")) == 4),
                (EXCHANGE_MAP.get(fget(207)), EXCHANGE_MAP.get(fget(207))),
                (EXCHANGE_MAP.get(fget(30)), EXCHANGE_MAP.get(fget(30))),
                (fget(207), fget(207)),
                (fget(30), fget(30)),
            ]
            if condition
        ),
        None,
    )


def strip_if_ends_alnum(val: str) -> str:
    return val[:2] if val[-1].isdigit() and val[-2].isupper() and val[-2].isalpha() else val


def open_figi_option_query(fget):
    expiration_date = date(fget(200), DTM_FMT) or ""
    body = dict(
        idType="ID_EXCH_SYMBOL",
        idValue=fget(48),
        currency=fget(15),
        securityType2="Option",
        optionType="Put" if fget(201) == "1" else "Call",
        strike=[fget(202, typ="double"), fget(202, typ="double")],
        expiration=[
            "-".join(expiration_date.split("-")[:2] + [fget(205)]),
            "-".join(expiration_date.split("-")[:2] + [fget(205)]),
        ],
    )
    if fget(207):
        body["exchCode"] = fget(207)

    try:
        response = open_figi_id(**body)
    except Exception as e:
        log(f"First figi lookup failed: {e}... Second Attempt:")
        body["idType"] = "BASE_TICKER"
        try:
            response = open_figi_id(**body)
        except Exception as e:
            log(f"Second figi lookup failed: {e}... Final Attempt:")
            prev_month = date(reduce_month(process_dtm(expiration_date, DTM_FMT))) or ""
            body["expiration"] = [
                "-".join(prev_month.split("-")[:2] + [fget(205)]),
                "-".join(expiration_date.split("-")[:2] + [fget(205)]),
            ]
            response = open_figi_id(**body)

    return response


def open_figi_id(**body) -> str:
    response = open_figi_lookup(**body)
    value = response.get("compositeFIGI") or response.get("figi")
    return value


def open_figi_lookup(**body) -> dict:
    value = None
    content = None
    try:
        response = requests.post(
            url="https://api.openfigi.com/v2/mapping",
            json=[body],
            headers={"X-OPENFIGI-APIKEY": os.environ.get("OPENFIGI_APIKEY")},
        )
        content = json.loads(response.content)
        if len(content) > 0 and len(content[0].get("data", list())) > 0:
            return content[0]["data"][0]
        log(f"FIGI lookup no data, Response: {content}, Body: {body}, Value: {value}")
        return {}
    except Exception as e:
        raise TransformExternalError(
            f"FIGI lookup error: {e}, Response: {content}, Body: {body}, Value: {value}"
        )


# resets the day so only for use where the day is not important.
def reduce_month(val: datetime) -> datetime:
    val = val.replace(day=1)
    if val.month > 1:
        return val.replace(month=val.month - 1)
    return val.replace(year=val.year - 1, month=12)


def get_side(fget) -> str:
    side = fget(54)
    if "FCS" in fget(448, all_vals=True) and fget(56, default="") == "MAP_FCSC_PROD":
        side = "2" if side in static.BUY else "1"
    return side


def price_notation(val):
    if val in ("PERC", "MONE", "BAPO", "YIEL"):
        return val
    elif val == "1":
        return "PERC"
    elif val == "3":
        return "MONE"
    elif val == "6":
        return "BAPO"
    elif val == "9":
        return "YIEL"


def prep_cache_instrument(response):
    instr = response[0]
    instr.pop("&id", None)
    instr.pop("&model", None)
    instr.pop("&timestamp", None)
    instr.pop("sourceKey", None)
    return instr
