CONDUCTOR_API_URL=https://conductor.dev-shared-2.steeleye.co/api
DATA_PLATFORM_CONFIG_API_URL=https://config-api.nonprod-eu-ie-1.steeleye.co
ELASTIC_HOST=elasticsearch.dev-shared-2.steeleye.co
ELASTIC_PORT=443
ELASTIC_SCHEME=https
ELASTIC_API_KEY=
STACK=dev-shared-2
SRP_ELASTIC_HOST=localhost
SRP_ELASTIC_PORT=9801
SRP_ELASTIC_SCHEME=http
TASK_NAME=order_tr_fidessa_eod
TASK_WORKER_DOMAIN=dev-shared-2
DEBUG=1
SRP_THROUGH_MASTER_DATA=False
OMA_REST_PROXY_URL=https://kafka-rest.uat-oma.steeleye.co
OMA_ARIES_TOPIC=aries.oma.events
AWS_PROFILE=nonprod_infra
COGNITO_AUTH_URL=https://dev-master-data.auth.eu-west-1.amazoncognito.com/oauth2/token
COGNITO_CLIENT_SECRET=13likcf06r341hnlr6fp8qp1gd550pm7jdkq71p3oj5rh34i2ouo
COGNITO_CLIENT_ID=627uaclfb5188f6ckimiup83i4
MARKET_DATA_API_URL=https://api.dev-market-data.steeleye.co
MASTER_DATA_API_HOST=https://api.dev-master-data.steeleye.co
MASTER_DATA_HOST=https://api.dev-master-data.steeleye.co
AZURE_STORAGE_CONNECTION_STRING=

# Used for order-feed-cfox
BATCH_SIZE=1000

# Used for order-flextrade-bell-potter-fix
DOWNLOAD_FILES_ASYNC=true
ON_PREMISES=false

# Used for order-blotter
# Azure
FETCH_MARKET_EOD_DATA=false