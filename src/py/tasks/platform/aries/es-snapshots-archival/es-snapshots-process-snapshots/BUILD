resource(name="config", source="config.yml")
python_sources(
    name="task",
    sources=["es_snapshots_process_snapshots/**/*.py"],
    dependencies=[
        ":config",
    ],
)

python_sources(
    name="sample",
    sources=["*.py"],
)

se_image(
    image_name="aries-es-snapshots-archival-process-snapshots",
    pex_entry_point="es_snapshots_process_snapshots/es_snapshots_process_snapshots_task.py:run",
)

resource(
    name="test_config",
    source="test_es_snapshots_process_snapshots/es_snapshots_process_snapshots_test_config.yml",
)
python_tests(
    name="tests",
    sources=["test_es_snapshots_process_snapshots/**/test_*.py"],
    dependencies=[":test_config"],
)
