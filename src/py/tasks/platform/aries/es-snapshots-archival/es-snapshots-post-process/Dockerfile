ARG SE_MONO_PY11_BASE_SUPERUSER
FROM $SE_MONO_PY11_BASE_SUPERUSER

ARG TARGETPLATFORM

# Install AWS CLI and zstd
RUN apt update && apt install -y --no-install-recommends \
    curl unzip zstd ca-certificates && \
    case "${TARGETPLATFORM}" in \
        "linux/amd64") ARCH="x86_64" ;; \
        "linux/arm64") ARCH="aarch64" ;; \
        *) echo "Unsupported platform: ${TARGETPLATFORM}" && exit 1 ;; \
    esac && \
    curl -fsSL --retry 5 "https://awscli.amazonaws.com/awscli-exe-linux-${ARCH}.zip" -o "awscliv2.zip" && \
    unzip -q awscliv2.zip && ./aws/install && \
    rm -rf awscliv2.zip aws /var/lib/apt/lists/*

# switch to non-root user before copying pex
USER app
# ensure that following ENTRYPOINT, COPY, ARG, and ENV statements are always added to the end of dockerfile in this order for optimal caching
ENTRYPOINT ["/bin/app/__main__.py"]
COPY --chown=app:app src.py.tasks.platform.aries.es-snapshots-archival.es-snapshots-post-process/bin.pex /bin/app
ARG IMAGE_TAG
ENV SE_VERSION=$IMAGE_TAG
