from aries_config_api_httpschema.zoom import ZoomPhoneConfigUpdate
from data_platform_config.containers import Container
from data_platform_config.services.zoom_phone_config import ZoomPhoneService
from dependency_injector.wiring import Provide, inject
from fastapi import APIRouter, Depends, Query, status

zoom_phone_router = APIRouter()

zoom_phone_config_router = APIRouter()


@zoom_phone_router.get("")
@inject
def get_config(
    stack_name: str,
    tenant_name: str,
    service: ZoomPhoneService = Depends(Provide[Container.zoom_phone_service]),
):
    """Gets configuration for zoom phone data source for given tenant.

    :param stack_name:
    :param tenant_name:
    :param service:
    :return:
    """
    return service.get_config(stack_name=stack_name, tenant_name=tenant_name)


@zoom_phone_router.put("", status_code=status.HTTP_201_CREATED)
@inject
def upsert_configuration(
    stack_name: str,
    tenant_name: str,
    zoom_phone_config_update: ZoomPhoneConfigUpdate,
    service: ZoomPhoneService = Depends(Provide[Container.zoom_phone_service]),
):
    """Upsert configuration which should belong to the tenant.

    :param tenant_name:
    :param zoom_phone_config_update:
    :param service:
    :return:
    """
    return service.upsert_config(
        stack_name=stack_name,
        tenant_name=tenant_name,
        zoom_phone_config_update=zoom_phone_config_update,
    )


@zoom_phone_router.delete("")
@inject
def delete_configuration(
    stack_name: str,
    tenant_name: str,
    service: ZoomPhoneService = Depends(Provide[Container.zoom_phone_service]),
):
    """deletes the configuration, if configuration exists for it.

    :param stack_name:
    :param tenant_name:
    :param subscription_delete:
    :param service:
    :return:
    """
    return service.delete_config(stack_name=stack_name, tenant_name=tenant_name)


@zoom_phone_config_router.get("")
@inject
def search_config(
    account_id: str | None = Query(default=None),
    service: ZoomPhoneService = Depends(Provide[Container.zoom_phone_service]),
):
    """Gets configuration for zoom phone data source for given account.

    :param account_id:
    :param service:
    :return:
    """
    return service.search_config(account_id=account_id)
