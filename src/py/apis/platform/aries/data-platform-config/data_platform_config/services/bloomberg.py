from aries_config_api_httpschema.bloomberg import BloombergConfigAdded
from data_platform_config.exceptions import DataNotFound
from data_platform_config.models.bloomberg.bloomberg_config import BloombergConfig
from data_platform_config.models.system.stack import Stack
from data_platform_config.models.system.tenant import Tenant
from sqlalchemy import insert
from typing import Any


class BloombergService:
    def __init__(self, session_factory) -> None:
        self.session_factory = session_factory

    def get_config_by_tenant(self, stack_name: str, tenant_name: str) -> list[BloombergConfig]:
        with self.session_factory() as session:
            configs: list[BloombergConfig] = (
                session.query(BloombergConfig)
                .join(Tenant, Stack)
                .filter(Stack.name == stack_name)
                .filter(Tenant.name == tenant_name)
                .all()
            )
            return configs

    def get_config_by_username(
        self, stack_name: str, tenant_name: str, username: str
    ) -> BloombergConfig:
        with self.session_factory() as session:
            return (  # type: ignore
                session.query(BloombergConfig)
                .join(Tenant, Stack)
                .filter(Stack.name == stack_name)
                .filter(Tenant.name == tenant_name)
                .filter(BloombergConfig.username == username)
                .one()
            )

    def add_config(self, stack_name: str, tenant_name: str, config: BloombergConfigAdded):
        with self.session_factory() as session:
            # verify if tenant exists or not:
            tenant = (
                session.query(Tenant)
                .join(Stack)
                .filter(Stack.name == stack_name)
                .filter(Tenant.name == tenant_name)
                .one_or_none()
            )
            if not tenant:
                raise DataNotFound(f"Tenant: {tenant_name} does not exist")

            for _each_config in config.resources:
                each_config_update: dict[str, Any] = _each_config.dict(exclude_unset=True)

                values = {
                    "username": each_config_update["username"],
                    "tenant_id": tenant.id,
                }
                insert_stmt = insert(BloombergConfig).values(**values)  # type: ignore
                session.execute(insert_stmt)

            session.commit()
