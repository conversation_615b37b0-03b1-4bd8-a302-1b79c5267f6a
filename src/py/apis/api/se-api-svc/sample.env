DEBUG=1 # Disable for prod
PORT=8080 # Doesn't matter in prod
EMAIL_DISABLED=0 # Disable in dev
FILECACHE_ENABLED=0
ELASTIC_APM_ENABLED=0 # Disable in dev

# Environment settings
ENVIRONMENT=dev-blue
CLOUD_PROVIDER=AWS

SE_REALM=
DEFAULT_REALM=

# Elasticsearch
ELASTIC_PORT=9200
ELASTIC_URL=https://elasticsearch.benchmark-blue.steeleye.co
SRP_ELASTIC_HOST="127.0.0.1"
SRP_ELASTIC_PORT=9001


# Postgres DB URLs
TENANT_DB_PG_URL=


# OpenAI
OPENAI_API_KEY=
OPENAI_API_BASE=
OPENAI_API_ENGINE=
OPENAI_API_TYPE=
OPENAI_API_VERSION=

# Kafka
KAFKA_PROXY_HOST=
KAFKA_PROXY_PORT=
KAFKA_PROXY_SSL=
KAFKA_IMAGE=

# Master data API
MASTER_DATA_API_COGNITO_AUTH_URL=
MASTER_DATA_API_COGNITO_CLIENT_ID=
MASTER_DATA_API_COGNITO_CLIENT_SECRET=
MASTER_DATA_API_MASTER_DATA_API_HOST=

# Misc
DATA_PLATFORM_CONFIG_API_URL=https://aries-platform-config.dev-enterprise.steeleye.co

# Redis
REDIS_HOST = ConfigVar("REDIS_HOST", default=None)
REDIS_PASSWORD = ConfigVar("REDIS_PASSWORD", default=None)
REDIS_PORT = ConfigVar("REDIS_PORT", default=6379)
REDIS_SSL = ConfigVar("REDIS_SSL", cast=bool, default=True)

# Sentry
SENTRY_ENABLED=0 # Disable for dev
SENTRY_URL=