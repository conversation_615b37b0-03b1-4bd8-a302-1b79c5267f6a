from api_sdk.di.request import ReqDep
from api_sdk.messages.fastapi_message_bus import FastApiMessageBus
from api_sdk.models.request_params import PaginatedDatedListParams
from api_sdk.utils.utils import b64decode_urlsafe_id
from fastapi import APIRouter, Depends, Query
from se_api_svc.api.routes.surveillance.comms import (
    alerts,
    classification,
    queries,
    threads,
    users,
    watches,
)
from se_api_svc.api.routes.surveillance.settings import create_notification_settings_router
from se_api_svc.messages.surveillance.events import AlertViewed
from se_api_svc.repository.surveillance.comms_alerts import CommunicationAlertRepository
from se_api_svc.schemas.settings import SettingsModule
from se_elastic_schema.static.surveillance import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from typing import Optional

router = APIRouter()

router.include_router(alerts.router, prefix="/alerts", tags=["surveillance/comms/alerts"])
router.include_router(classification.router, tags=["surveillance/comms/classification"])
router.include_router(
    create_notification_settings_router(
        module=SettingsModule.COMMS_SURVEILLANCE, name_prefix="surveillance:comms"
    ),
    prefix="/settings/notifications",
    tags=["surveillance/comms/settings/notifications"],
)
router.include_router(queries.router, prefix="/queries", tags=["surveillance/comms/queries"])
router.include_router(
    threads.router,
    prefix="",
    tags=["surveillance/comms/threads"],
)
router.include_router(
    users.router,
    prefix="/users",
    tags=["surveillance/comms/users"],
)
router.include_router(
    watches.router,
    prefix="/watches",
    tags=["surveillance/comms/watches"],
)


@router.get(
    path="/{encoded_comm_id}/alerts",
    name="surveillance:alerts:get-alerts-by_comm_id",
    summary="Get Alerts",
)
async def get_alerts_by_comm_id(
    encoded_comm_id: str,
    query_kind: Optional[BehaviourQueryKind] = Query(None, alias="queryKind"),
    page_params: PaginatedDatedListParams = Depends(),
    repo: CommunicationAlertRepository = ReqDep(CommunicationAlertRepository),
    mb: FastApiMessageBus = Depends(),
):
    comm_id = b64decode_urlsafe_id(encoded_comm_id, fallback=True)

    alerts = await repo.get_alerts_with_watches(
        record_id=comm_id,
        query_kind=query_kind,
        only_ids=True,
        **page_params.as_search_kwargs(),
    )

    await mb.publish(AlertViewed(alerts=alerts.hits.hits))

    return alerts.hits
