from api_sdk.auth import Tenancy
from api_sdk.config_base import ApiConfig
from api_sdk.di.request import ReqDep
from api_sdk.responses import OkResponse
from fastapi import APIRouter
from se_api_svc.repository.audit import AuditRepository
from se_api_svc.schemas.track import UserAudit, UserAuditIn, UserError, UserErrorIn
from starlette.background import BackgroundTasks

router = APIRouter()


@router.post("/error", name="track:error")
async def error_audit(
    user_error: UserErrorIn,
    background_tasks: BackgroundTasks,
    track: AuditRepository = ReqDep(AuditRepository),
    tenancy: Tenancy = ReqDep(Tenancy),
    config: ApiConfig = ReqDep(ApiConfig),
):
    user_error_dict = user_error.to_dict()
    user_error_dict["user"] = tenancy.userId
    user_error_dict["userName"] = tenancy.user_name
    user_error_dict["realm"] = tenancy.realm
    if not config.AUDIT_DISABLED:
        background_tasks.add_task(track.write_audit, UserError, **user_error_dict)
    return OkResponse()


@router.post("/route", name="track:route")
async def route_audit(
    user_audit: UserAuditIn,
    background_tasks: BackgroundTasks,
    track: AuditRepository = ReqDep(AuditRepository),
    tenancy: Tenancy = ReqDep(Tenancy),
    config: ApiConfig = ReqDep(ApiConfig),
):
    user_audit_dict = user_audit.to_dict()
    user_audit_dict["user"] = tenancy.userId
    user_audit_dict["userName"] = tenancy.user_name
    user_audit_dict["realm"] = tenancy.realm
    if not config.AUDIT_DISABLED:
        background_tasks.add_task(track.write_audit, UserAudit, **user_audit_dict)
    return OkResponse()


@router.post("/search", name="track:search")
async def search_audit(
    user_audit: UserAuditIn,
    background_tasks: BackgroundTasks,
    track: AuditRepository = ReqDep(AuditRepository),
    tenancy: Tenancy = ReqDep(Tenancy),
    config: ApiConfig = ReqDep(ApiConfig),
):
    user_audit_dict = user_audit.to_dict()
    user_audit_dict["user"] = tenancy.userId
    user_audit_dict["userName"] = tenancy.user_name
    user_audit_dict["realm"] = tenancy.realm
    if not config.AUDIT_DISABLED:
        background_tasks.add_task(track.write_audit, UserAudit, **user_audit_dict)
    return OkResponse()
