import datetime as dt
import pydantic
from api_sdk.schemas.base import APIModel, RecordModel
from api_sdk.utils.utils import StringEnum
from enum import auto
from pydantic import Field
from se_elastic_schema.components.provenance.record_identifer import RecordIdentifer
from se_elastic_schema.components.provenance.s3_object_stats import S3ObjectStats
from se_elastic_schema.components.provenance.sink_record_validation import SinkRecordValidation
from se_elastic_schema.static.provenance import SinkRecordStatus, TaskStatus
from typing import Dict, List, Optional


class QuarantinedOrder(RecordModel):
    class Config:
        model_name = "QuarantinedOrder"
        index_suffix = "quarantined_order"
        trait_fqn = "mifid2/order"

        extra = pydantic.Extra.allow


class QuarantinedRts22Transaction(RecordModel):
    class Config:
        model_name = "QuarantinedRTS22Transaction"
        index_suffix = "quarantined_rts22_transaction"
        trait_fqn = "mifid2/rts22_transaction"

        extra = pydantic.Extra.allow


class QuarantinedTransaction(RecordModel):
    class Config:
        model_name = "QuarantinedTransaction"
        index_suffix = "quarantined_transaction"
        trait_fqn = "mifid2/transaction"

        extra = pydantic.Extra.allow


class CascadeAudit(RecordModel):
    class Config:
        model_name = "CascadeAudit"
        index_suffix = "cascade_audit"
        trait_fqn = "provenance/cascade_audit"

        extra = pydantic.Extra.allow

    action: str
    cascadeId: str
    completed: Optional[dt.datetime] = None
    duration: Optional[dt.time] = None
    queued: dt.datetime
    realm: str
    recordsAffected: Optional[Dict[str, int]] = None
    sourceId: Optional[str] = None
    sourceModel: str = None
    sourceTimestamp: Optional[str] = None
    started: Optional[dt.datetime] = None
    status: Optional[TaskStatus] = None
    taskError: Optional[str] = None
    triggeredBy: Optional[str] = None


class SinkAudit(RecordModel):
    class Config:
        model_name = "SinkAudit"
        index_suffix = "sink_audit"
        trait_fqn = "provenance/sink_audit"

        extra = pydantic.Extra.allow


class SinkFileAudit(RecordModel):
    s3_object: Optional[S3ObjectStats] = Field(None, alias="s3_object")

    class Config:
        model_name = "SinkFileAudit"
        index_suffix = "sink_file_audit"
        trait_fqn = "provenance/sink_file_audit"

        extra = pydantic.Extra.allow


class SinkRecordAudit(RecordModel):
    class Config:
        model_name = "SinkRecordAudit"
        index_suffix = "sink_record_audit"
        trait_fqn = "provenance/sink_record_audit"

        extra = pydantic.Extra.allow

    bucket: str = None
    dataSourceName: Optional[str] = None
    index: Optional[str] = None
    key: str
    matchedKey: Optional[str] = None
    processed: Optional[dt.datetime] = None
    recordIdentifiers: Optional[List[RecordIdentifer]] = None
    recordKey: Optional[str] = None
    recordType: Optional[str] = None
    status: SinkRecordStatus
    taskStarted: Optional[dt.datetime] = None
    userProcessed: Optional[bool] = None
    validationErrors: Optional[List[SinkRecordValidation]] = None

    numberOfAmends: Optional[int]  # TODO this field is not mentioned in se_schema.


class CascadeAuditTrendChart(StringEnum):
    ACTION = auto()
    MODEL = auto()
    REALM = auto()
    STATUS = auto()
    TRIGGERED_BY = auto()


class SinkAuditTrendChart(StringEnum):
    ERROR_CODE = auto()
    ERROR_MESSAGE = auto()
    EVENT_TYPE = auto()
    FUNCTION = auto()
    INPUT = auto()
    MESSAGE = auto()
    MODEL = auto()
    STAGE = auto()
    TENANT = auto()


class SinkFileAuditTrendChart(StringEnum):
    BUCKET = auto()
    DATA_SOURCE = auto()
    KEY = auto()
    STATUS = auto()
    ERROR_CLASS = auto()


class RecordAuditType(StringEnum):
    SINK_FILE_AUDIT = auto()


class SinkRecordAuditTrendChart(StringEnum):
    BUCKET = auto()
    DATA_SOURCE_NAME = auto()
    ERROR_CLASS = auto()
    ERROR_MESSAGE = auto()
    INDEX = auto()
    MODEL = auto()
    STATUS = auto()


class ProcessQuarantinedRecordsIn(APIModel):
    ids: List[str]


class ReconFlow(StringEnum):
    NCA = "three-way-rec-nca-fca"
    ARM = "three-way-rec-arm-unavista"
