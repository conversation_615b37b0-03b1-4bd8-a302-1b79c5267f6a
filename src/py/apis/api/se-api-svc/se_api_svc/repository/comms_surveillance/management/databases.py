# type: ignore
import logging
import uuid
from abc import ABC, abstractmethod
from api_sdk.auth import Tenancy
from datetime import datetime
from enum import Enum
from se_api_svc.core.config import ApiServiceConfig
from se_api_svc.messages.account.events import UserChange
from se_api_svc.repository.comms_surveillance.management.static import language_mappings
from se_api_svc.utils.attributes import get_attr
from se_db_utils.database import Database
from sqlalchemy import INTEGER, TEXT, Boolean, Integer, String, and_, case, or_
from sqlalchemy import Enum as SqlEnum
from sqlalchemy.dialects.postgresql import ARRAY
from sqlalchemy.orm import Session
from tenant_db.declarative_base import Base
from typing import Dict, List, Optional, Tuple, TypeVar

ModelType = TypeVar("ModelType", bound=Base)

log = logging.getLogger(__name__)


class BaseDBManager(ABC):
    _session_factory = None
    config: ApiServiceConfig
    sessions: Dict[str, Session] = {}

    def __init__(self, tenancy: Tenancy, db: Database, tenant_schema: str = None) -> None:
        self.tenancy = tenancy
        self.tenant_schema = tenant_schema if tenant_schema else self.tenancy.tenant
        self.db = db

    def get_session(self) -> Session:
        with self.db.session(tenant_schema=self.tenant_schema) as session:
            return session

    @abstractmethod
    def get(self, id: str, session: Session = None) -> ModelType:
        close_session = False
        if not session:
            session = self.get_session()
            close_session = True
        try:
            return (
                session.query(self.model)
                .filter(self.model.tenant == self.tenancy.tenant, self.model.id == id)
                .first()
            )
        except Exception as e:
            session.rollback()
            logging.error(e)
        finally:
            if close_session:
                session.close()

    @abstractmethod
    def get_all(
        self,
        model,
        filters: dict = None,
        sort: str = None,
        take: int = 50,
        skip: int = 0,
        search: str = None,
        search_tables: List[str] = None,
    ) -> Tuple[List[dict], dict]:
        with self.get_session() as session:
            try:
                query = session.query(model).filter(self.model.tenant == self.tenancy.tenant)
                query = self.filter(query, filters)
                query = self.sort(query, sort)
                query = self.search(query, search_term=search, search_tables=search_tables)
                query, headers = self.paginate(query, take=take, skip=skip)
                results = query.all()
                return self.to_dict_list(results), headers
            except Exception as e:
                session.rollback()
                logging.error(e)

    @abstractmethod
    def create(self, obj_in: Base) -> ModelType:
        pass

    @abstractmethod
    def update(
        self,
        db_obj: ModelType,
        obj_in: Base,
        session: Session,
        with_changes: Optional[bool] = False,
    ) -> ModelType:
        update_data = obj_in.dict(exclude_unset=True)
        changes = []
        if with_changes:
            changes = self.get_record_changes(old_record=db_obj, new_record=update_data)

        for key, value in update_data.items():
            setattr(db_obj, key, value)

        db_obj = self.add_update_attr(db_obj)
        try:
            session.commit()
            session.refresh(db_obj)

            if with_changes:
                return (db_obj, changes)

            return db_obj
        except Exception as e:
            session.rollback()
            logging.error(e)

    @abstractmethod
    def delete(self, db_obj: ModelType, session: Session, hard_delete: bool = False) -> ModelType:
        if hard_delete:
            session.delete(db_obj)
        else:
            setattr(db_obj, "retired", True)
            db_obj = self.add_update_attr(db_obj)
        try:
            session.commit()
            if not hard_delete:
                session.refresh(db_obj)
            return db_obj
        except Exception as e:
            session.rollback()
            logging.exception(e)

    @abstractmethod
    def undelete(self, db_obj: ModelType, session: Session) -> ModelType:
        setattr(db_obj, "retired", False)
        db_obj = self.add_update_attr(db_obj)
        try:
            session.commit()
            session.refresh(db_obj)
            return db_obj
        except Exception as e:
            session.rollback()
            logging.error(e)

    def if_exists(self, model, criteria: dict) -> bool:
        with self.get_session() as session:
            try:
                query = session.query(model).filter(model.tenant == self.tenancy.tenant)
                query = self.filter(query, criteria)
                return query.first() is not None
            except Exception as e:
                session.rollback()
                logging.error(e)

    def filter(self, query, filters: dict, model: ModelType = None):
        if not model:
            model = self.model
        if filters:
            for filter_key, filter_val in filters.items():
                if hasattr(model, filter_key):
                    column = getattr(model, filter_key)
                    if isinstance(filter_val, str):
                        if filter_val and filter_val.strip():
                            if filter_val.startswith("*") and filter_val.endswith("*"):
                                # If the filter value is surrounded
                                # by asterisks, perform a substring search
                                query = query.filter(column.ilike(f"%{filter_val[1:-1]}%"))
                            elif filter_val.startswith("*"):
                                # If the filter value starts with an
                                # asterisk, perform an ends with search
                                query = query.filter(column.ilike(f"%{filter_val[1:]}"))
                            elif filter_val.endswith("*"):
                                # If the filter value ends with an asterisk,
                                # perform a starts with search
                                query = query.filter(column.ilike(f"{filter_val[:-1]}%"))
                            else:
                                # Otherwise, perform an exact match search
                                query = query.filter(column == filter_val)
                        else:
                            # If the filter value is an empty string or whitespace, skip the filter
                            continue
                    elif isinstance(filter_val, bool):
                        query = query.filter(column.is_(filter_val))
                    elif isinstance(filter_val, list):
                        query = query.filter(column.in_(filter_val))
                    elif isinstance(filter_val, Enum):
                        query = query.filter(column == filter_val.value)
                    elif isinstance(filter_val, uuid.UUID):
                        query = query.filter(column == filter_val)
        return query

    def paginate(self, query, skip: int = 0, take: int = 50) -> Tuple[dict]:
        total = query.count()
        query = query.offset(skip).limit(take)
        returned = query.count()
        skipped = skip if skip is not None else 0
        if total == 0:
            skipped = 0
        headers = self.create_response_headers(total, skipped, returned)
        return query, headers

    @staticmethod
    def create_response_headers(total: int, skipped: int, returned: int) -> dict:
        return {"totalHits": str(total), "returnedHits": str(returned), "skippedHits": str(skipped)}

    def sort(self, query, sort: str = None, model: ModelType = None):
        if not sort:
            return query

        model = model or self.model
        # Parse sort parameter
        if ":" in sort:
            column, direction = sort.split(":")
        else:
            column, direction = sort, "asc"

        if column == "language":
            language_expression = case(
                [(model.language == key, value) for key, value in language_mappings.items()],
                else_=model.language,
            )
            column_expression = language_expression
        else:
            column_expression = getattr(model, column, None)

        if column_expression is None:
            return query

        order_func = column_expression.asc if direction == "asc" else column_expression.desc
        return query.order_by(order_func())

    @staticmethod
    def to_dict(model_obj: ModelType, fields: List[str] = []) -> dict:
        result = {}
        for key, value in model_obj.__dict__.items():
            if key == "_sa_instance_state":
                continue
            if fields and key not in fields:
                continue
            if isinstance(value, uuid.UUID):
                result[key] = str(value)
            elif isinstance(value, datetime):
                result[key] = value.isoformat()
            else:
                result[key] = value
        return result

    def to_dict_list(self, model_objs: List[ModelType], fields: List[str] = []) -> List[dict]:
        return [self.to_dict(obj, fields=fields) for obj in model_objs]

    def add_update_attr(self, db_obj):
        setattr(db_obj, "updatedBy", self.tenancy.userId)
        setattr(db_obj, "updatedDateTime", datetime.now())
        return db_obj

    def search(self, query, search_term: str, search_tables: List[str]):
        if not search_term or not search_tables:
            return query

        filtered_queries = []
        tables = {table_name: Base.metadata.tables[table_name] for table_name in search_tables}

        search_term = search_term.replace("*", "%")

        for table in search_tables:
            for c in tables[table].columns:
                if isinstance(c.type, SqlEnum):
                    filtered_queries.append(c.cast(TEXT).ilike(search_term.upper()))
                elif isinstance(c.type, TEXT) or isinstance(c.type, String):
                    filtered_queries.append(c.ilike(search_term))
                elif (
                    isinstance(c.type, INTEGER)
                    or isinstance(c.type, Integer)
                    or isinstance(c.type, Boolean)
                    or isinstance(c.type, ARRAY)
                ):
                    filtered_queries.append(c.cast(TEXT).ilike(search_term))

        return query.filter(and_(or_(*filtered_queries)))

    def formatted_value(self, value) -> str:
        def to_str(item):
            if isinstance(item, Enum):
                return str(item.value).replace("_", " ").title()
            return str(item)

        if isinstance(value, list):
            return str([to_str(v) for v in value])
        elif value is None:
            return ""

        return to_str(value)

    def get_record_changes(self, old_record, new_record):
        changes = []

        _vars = new_record.keys() if isinstance(new_record, dict) else vars(new_record)
        for key in _vars:
            if get_attr(old_record, key) != get_attr(new_record, key):
                changes.append(
                    UserChange(
                        field=key,
                        old_value=self.formatted_value(get_attr(old_record, key)),
                        new_value=self.formatted_value(get_attr(new_record, key)),
                    )
                )

        return changes
