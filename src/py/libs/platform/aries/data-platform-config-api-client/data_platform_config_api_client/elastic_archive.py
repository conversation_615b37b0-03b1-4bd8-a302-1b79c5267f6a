import addict
import logging
from aries_se_api_client.base import EndPoint
from aries_se_api_client.client import AriesAbstractApiClient
from aries_se_api_client.response_parsers import AddictResponseParser
from data_platform_config_api_client.base import DataPlatformConfigAPI
from typing import Any, ClassVar

log = logging.getLogger(__name__)


class ElasticArchiveAPI(DataPlatformConfigAPI):
    PREFIX: ClassVar[str] = "/stacks/{stack_name}/tenants/{tenant_name}/elastic"
    CREATE_INDEX_ARCHIVE_METADATA: ClassVar[str] = "/index_archive_metadata"
    GET_INDEX_ARCHIVE_METADATA: ClassVar[str] = "/index_archive_metadata/{index_name}"
    UPDATE_INDEX_RESTORE_METADATA: ClassVar[str] = "/index_restore_metadata/{index_name}"
    UPDATE_INDEX_ARCHIVE_METADATA: ClassVar[str] = "/index_archive_metadata/{index_name}"

    def get_prefix(self) -> str:
        return super().get_prefix() + ElasticArchiveAPI.PREFIX

    def __init__(self, client: AriesAbstractApiClient):
        self._client = client

        self._create_index_archive_metadata: EndPoint[addict.Dict] = EndPoint(
            path=ElasticArchiveAPI.CREATE_INDEX_ARCHIVE_METADATA,
            http_verb="POST",
            response_parser=AddictResponseParser(),
        )

        self._get_index_archive_metadata: EndPoint[addict.Dict] = EndPoint(
            path=ElasticArchiveAPI.GET_INDEX_ARCHIVE_METADATA,
            http_verb="GET",
            response_parser=AddictResponseParser(),
        )

        self._update_index_restore_metadata: EndPoint[addict.Dict] = EndPoint(
            path=ElasticArchiveAPI.UPDATE_INDEX_RESTORE_METADATA,
            http_verb="PUT",
            response_parser=AddictResponseParser(),
        )

        self._update_index_archive_metadata: EndPoint[addict.Dict] = EndPoint(
            path=ElasticArchiveAPI.UPDATE_INDEX_ARCHIVE_METADATA,
            http_verb="PUT",
            response_parser=AddictResponseParser(),
        )

    def create_index_archive_metadata(
        self,
        json_body: dict[str, Any],
        stack_name: str,
        tenant_name: str,
    ):
        return self._client.call_api(
            api=self,
            endpoint=self._create_index_archive_metadata,
            json_body=json_body,
            path_params={
                "stack_name": stack_name,
                "tenant_name": tenant_name,
            },
        )

    def get_index_archive_metadata(self, stack_name: str, tenant_name: str, index_name: str):
        return self._client.call_api(
            api=self,
            endpoint=self._get_index_archive_metadata,
            path_params={
                "stack_name": stack_name,
                "tenant_name": tenant_name,
                "index_name": index_name,
            },
        )

    def update_index_restore_metadata(
        self,
        json_body: dict[str, Any],
        stack_name: str,
        tenant_name: str,
        index_name: str,
    ):
        return self._client.call_api(
            api=self,
            endpoint=self._update_index_restore_metadata,
            json_body=json_body,
            path_params={
                "stack_name": stack_name,
                "tenant_name": tenant_name,
                "index_name": index_name,
            },
        )

    def update_index_archive_metadata(
        self,
        json_body: dict[str, Any],
        stack_name: str,
        tenant_name: str,
        index_name: str,
    ):
        return self._client.call_api(
            api=self,
            endpoint=self._update_index_archive_metadata,
            json_body=json_body,
            path_params={
                "stack_name": stack_name,
                "tenant_name": tenant_name,
                "index_name": index_name,
            },
        )
