from aries_io_event.app_metric import AppMetricFieldSet
from aries_io_event.base_model import IOEventBaseModel
from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.task_metric import TaskMetricFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from pydantic import Field


class IOEvent(IOEventBaseModel):
    workflow: WorkflowFieldSet = Field(..., description="Workflow information")
    task: TaskFieldSet = Field(..., description="Task information")
    app_metric: AppMetricFieldSet | None = Field(
        default=None, description="Contains app specific metrics, which are set by the tasks"
    )
    task_metric: TaskMetricFieldSet | None = Field(
        default=None,
        description="Contains task specific metrics, like execution time. "
        "These are set by aries-link task wrapper",
    )
    io_param: IOParamFieldSet | None = Field(
        default=None,
        description="Input and output parameter of each task. This can be"
        "used by tasks to set parameters used in subsequent"
        "tasks. All params from previous task are sent as "
        "argument to the task",
    )
