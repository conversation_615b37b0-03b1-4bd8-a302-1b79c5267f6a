from pydantic import BaseSettings, Field, root_validator


class OpenAIConfig(BaseSettings):
    """This class is a global settings config for OpenAI."""

    OPENAI_BACKOFF_MAX_TIME: int | None = Field(
        600, description="Maximum time (in seconds) till which we retry a call."
    )
    OPENAI_MAX_ASYNC_REQUESTS_IN_A_BATCH: int = Field(
        5, description="Maximum number of asynchronous requests that can be made in a single batch."
    )
    OPENAI_MAX_RETRIES_PER_CALL: int | None = Field(
        None, description="Maximum number of retries allowed per API call."
    )
    OPENAI_MAX_USER_PROMPT_TOKENS: int = Field(
        122000, description="Maximum size (in tokens) allowed for the LLM prompt"
    )
    OPENAI_MAX_WAIT_TIME_PER_RETRY: int = Field(
        120, description="Maximum wait time (in seconds) between retries"
    )
    OPENAI_REQUEST_PER_MIN: int = Field(
        3000, description="Maximum number of requests allowed per minute to the OpenAI API."
    )
    OPENAI_RETRY_FACTOR: float = Field(
        1.2, description="Determines how the wait time is modified between retries."
    )
    OPENAI_WAIT_TIME_PER_RETRY: int = Field(
        60, description="Initial wait time (in seconds) between retries."
    )
    OPENAI_USE_CUSTOM_RETRY: bool = Field(
        True,
        description=(
            "Flag indicating whether to use custom retry logic. "
            "Valid values are: '1', 't', 'T', 'true', 'True', 'TRUE', "
            "and '0', 'f', 'F', 'false', 'False', 'FALSE'."
        ),
    )
    OPENAI_API_BASE: str = Field(..., description="Base URL for the OpenAI API.")
    OPENAI_API_KEY: str = Field(
        ..., description="API key for authenticating requests to the OpenAI API."
    )
    OPENAI_API_MODEL: str = Field(..., description="Specifies the OpenAI model to be used.")
    OPENAI_API_VERSION: str = Field(
        ..., description="Specifies the version of the OpenAI API to be used."
    )
    TOKENIZER_MODEL: str = "gpt-4o"

    HOSTED_LLM_MAX_WAIT_TIME: int = Field(
        1200, description="Max wait time for vllm to spin up in seconds"
    )
    HOSTED_LLM_REQUEST_INTERVAL: int = Field(
        30, description="Interval between pings for vllm spinup in seconds"
    )
    HOSTED_LLM: bool = Field(
        False, description="Explicit self hosted variable for when not using openapi/gpt-4o"
    )

    @root_validator
    def validate_backoff_params(cls, values):
        if values.get("OPENAI_MAX_RETRIES_PER_CALL") and values.get("OPENAI_BACKOFF_MAX_TIME"):
            raise ValueError(
                "Both `OPENAI_MAX_RETRIES_PER_CALL` and `OPENAI_BACKOFF_MAX_TIME` "
                "cannot be populated. If you want a count based retry "
                "populate `OPENAI_MAX_RETRIES_PER_CALL`. If you want a time "
                "elapsed based retry populate `OPENAI_BACKOFF_MAX_TIME`"
            )

        return values

    @root_validator
    def validate_custom_retry_params(cls, values):
        if not values.get("OPENAI_USE_CUSTOM_RETRY") and not values.get(
            "OPENAI_MAX_RETRIES_PER_CALL"
        ):
            raise ValueError(
                "When `OPENAI_USE_CUSTOM_RETRY` is False "
                "`OPENAI_MAX_RETRIES_PER_CALL` must be populated"
            )

        return values


OPENAI_CONFIG = OpenAIConfig()
