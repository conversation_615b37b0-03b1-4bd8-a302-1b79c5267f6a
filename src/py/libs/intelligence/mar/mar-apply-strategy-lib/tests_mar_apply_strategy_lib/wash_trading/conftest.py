import os
import pytest
from se_elastic_schema.static.surveillance import MarketAbuseReportType
from surveillance_utils.test_mock_helpers import FakeSequenceService
from tests_mar_apply_strategy_lib.shared_mar_mocks import (
    Helpers,
    mm_base_envvars,
    mock_apply_strategy_generic,
)
from unittest.mock import MagicMock, patch

# import mandatory envvars
mm_base_envvars()


@pytest.fixture
def mock_apply_strategy_kwargs():
    return mock_apply_strategy_generic(MarketAbuseReportType.WASH_TRADING)


@pytest.fixture
def helpers():
    return Helpers


@pytest.fixture
def skip_test_in_ci():
    if not os.environ.get("CI_TEST_MODE") == "false":
        raise pytest.skip("Test doesn't run in CI.")


@pytest.fixture()
def fake_sequence_service():
    return FakeSequenceService


@pytest.fixture(autouse=True)
def mock_upload():
    with patch("se_fsspec_utils.file_utils.upload") as mock:
        # Customize return value if needed
        mock.return_value = MagicMock()  # or mock.return_value = "fake_upload_result"
        yield mock
