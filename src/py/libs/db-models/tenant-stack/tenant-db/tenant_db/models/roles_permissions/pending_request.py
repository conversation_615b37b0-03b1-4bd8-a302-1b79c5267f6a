import uuid
from se_db_utils.psql_utils import utcnow
from sqlalchemy import (
    Column,
    DateTime,
    ForeignKeyConstraint,
    PrimaryKeyConstraint,
    String,
    Text,
)
from sqlalchemy.dialects.postgresql import JSONB, UUID
from tenant_db.declarative_base import Base


class PendingRequest(Base):
    __tablename__ = "PendingRequest"

    id = Column(UUID(as_uuid=True), default=uuid.uuid4)
    userId = Column(String, nullable=False)
    # Request data.
    # URL, METHOD, HEADERS, BODY, BODY_TYPE
    request = Column(JSONB, nullable=True)
    # ToDo: Remove string literal
    status = Column(String, nullable=False, server_default="PENDING")  # PENDING, APPROVED, REJECTED

    # Foreign Keys
    permissionId = Column(UUID(as_uuid=True), nullable=False)
    module = Column(String, nullable=False)
    subModule = Column(String, nullable=False)

    # Metadata
    createdDateTime = Column(DateTime, nullable=False, server_default=utcnow())
    updatedDateTime = Column(DateTime, onupdate=utcnow())
    createdBy = Column(Text, nullable=True)
    updatedBy = Column(Text, nullable=True)

    __table_args__ = (
        PrimaryKeyConstraint("id", name="pk_PendingRequest"),
        ForeignKeyConstraint(
            ["permissionId", "module", "subModule"],
            [
                "ModulePermission.permissionId",
                "ModulePermission.module",
                "ModulePermission.subModule",
            ],
            name="fk_PendingRequest_permissionId_ModulePermission",
        ),
    )
