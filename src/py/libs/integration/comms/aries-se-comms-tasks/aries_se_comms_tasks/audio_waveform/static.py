class AudioExtension:
    WAV = ".wav"


class TempColumns:
    FILE_SIZE = "__file_size__"
    TEMP_VOICE_FILE_PREFIX = "/voice_file"
    TEMP_VOICE_FILE_TRANSCODED = "/voice_file_transcoded.wav"


class TargetColumns:
    DURATION_IN_SECONDS = "duration_in_seconds"
    WAVEFORM_DATA = "waveform_data"


class WaveFormGeneratorSourceCols:
    CALL_ID = "CALL_ID"
    MEETING_ID = "MEETING_ID"
    VOICE_FILE_URL = "VOICE_FILE_URL"
    SOURCE_FEED = "SOURCE_FEED"


UNKNOWN_SOURCE_FEED = "unknown_source_feed"

WAVEFORM_FLOW_BATCH_PRODUCER_SCHEMA = {
    "CALL_ID": "string",
    "MEETING_ID": "string",
    "SOURCE_FEED": "string",
    "VOICE_FILE_URL": "string",
}
