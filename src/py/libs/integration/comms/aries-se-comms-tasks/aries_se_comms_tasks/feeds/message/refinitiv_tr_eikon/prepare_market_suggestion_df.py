# type: ignore
import logging
import pandas as pd
from aries_se_comms_tasks.feeds.message.refinitiv_tr_eikon.static import (
    TR_EIKON_CHAT_USERS_SOURCE_SCHEMA,
    RefinitivTrEikonSourceColumns,
)
from aries_se_core_tasks.core.exception import TaskException
from aries_se_core_tasks.core.integration_task import IntegrationTask
from aries_se_core_tasks.utilities.elasticsearch_utils import (
    es_scroll,
    get_es_config,
    get_terms_query,
)
from se_elastic_schema.elastic_schema.core.steeleye_schema_model import SteelEyeSchemaBaseModelES8
from se_elastic_schema.models.tenant.account.account_firm import AccountFirm
from se_elastic_schema.models.tenant.account.account_person import AccountPerson
from se_elastic_schema.models.tenant.market.market_counterparty import MarketCounterparty
from se_elastic_schema.models.tenant.market.market_person import MarketPerson
from se_elasticsearch.repository import get_repository_by_cluster_version
from se_elasticsearch.repository.elasticsearch6 import ElasticsearchRepository
from typing import List, Optional, Tuple, Type

logger = logging.getLogger(__name__)


class FailIfEmptyDataFrames(TaskException):
    pass


class SkipIfMarketSuggestionsAlreadyLinked(TaskException):
    pass


TERMS_FIELD = "communications.emails"


class PrepareMarketSuggestionDf(IntegrationTask):
    """The input of this task is a List of DataFrames containing User data from
    several chats processed from a given TR Eikon input file.

    The goal is to identify exactly which records should be used to
    create MarketSuggestion records. Simply, if a user identifier
    (email) is not present in the tenant's ElasticSearch database, it
    means we need to create a MarketSuggestion record for it. The output
    of this Task is a List of DataFrames whose records correspond to
    User data that needs to be mapped to MarketSuggestion records. If
    there are no MarketSuggestion records to be created, a SKIP signal
    is raised to skip downstream tasks (the rest of the Flow is
    independent)
    """

    def _run(self, list_of_user_dataframes: List[pd.DataFrame], tenant: str) -> List[pd.DataFrame]:
        es_client: ElasticsearchRepository = get_repository_by_cluster_version(
            resource_config=get_es_config()
        )
        return self.process(
            list_of_user_dataframes=list_of_user_dataframes,
            tenant=tenant,
            es_client=es_client,
        )

    @classmethod
    def process(
        cls,
        list_of_user_dataframes: List[pd.DataFrame],
        tenant: str,
        es_client: ElasticsearchRepository,
    ) -> List[pd.DataFrame]:
        if not isinstance(list_of_user_dataframes, list):
            raise FailIfEmptyDataFrames(
                "Input is not a List of DataFrames, thus it cannot be processed"
            )

        for idx, df in enumerate(list_of_user_dataframes):
            logger.info(
                f"Input DataFrame has {df.shape[0]} records. Assessing"
                f" if they all need a MarketSuggestion record"
            )
            if df.empty:
                continue

            list_of_user_dataframes[idx] = cls.filter_out_unnecessary_market_suggestions(
                df=df, es_client=es_client, tenant=tenant
            )
            logger.info(
                f"Input DataFrame has {df.shape[0]} records absent from"
                f" the database that need a MarketSuggestion"
            )

        result = [df for df in list_of_user_dataframes if not df.empty]
        if not result:
            raise SkipIfMarketSuggestionsAlreadyLinked(
                "There are no records that require MarketSuggestion creation."
                " Skipping downstream tasks"
            )

        return result

    @classmethod
    def filter_out_unnecessary_market_suggestions(
        cls, df: pd.DataFrame, es_client, tenant: str
    ) -> pd.DataFrame:
        """Drop unnecessary input columns. Build identifiers from the user
        first and last name, plus their emails and check if there are any
        records in ElasticSearch that match them by the TERMS_FIELD field.
        Records that match with ElasticSearch data are immediately discarded.

        :param df: DataFrame that will be mutated by selecting a subset of columns
         and possibly a subset of rows
        :param es_client: ElasticSearch Client
        :param tenant: Tenant name used to identify which ElasticSearch index to
        scroll
        """

        df = df.loc[:, list(TR_EIKON_CHAT_USERS_SOURCE_SCHEMA.keys())]

        complete_name_series, identifiers_series = PrepareMarketSuggestionDf.get_id_series(
            source_frame=df
        )
        query_term_id_values = list(complete_name_series.dropna().values) + list(
            identifiers_series.dropna().values
        )

        logger.info(f"scrolling MyMarket records for {len(query_term_id_values)} identifiers")

        hit_mask = pd.Series(False, index=complete_name_series.index)
        for model in [AccountPerson, MarketPerson, AccountFirm, MarketCounterparty]:
            hits = cls.get_my_market_records_by_email(
                es_client=es_client,
                model=model,
                query_term_id_values=query_term_id_values,
                tenant=tenant,
            )

            # identifiers do not exist in ElasticSearch, therefore we need to create
            # MarketSuggestion records for it
            # note that we skip to next iteration because there's no records to filter
            # out from `df`
            if hits.empty:
                logger.info(f"Found no MyMarket records for the {model.schema()['title']} model")
                continue

            iteration_hit_mask = cls.identify_input_user_records_with_my_market_data(
                complete_name_series=complete_name_series,
                hits=hits,
                identifiers_series=identifiers_series,
                model=model,
            )

            # multiple hits by source id on different models is ok
            hit_mask[iteration_hit_mask] = True

            # if we linked every input identifier with myMarket data, we're done here
            if hit_mask.all():
                break

        return df.loc[~hit_mask, :]

    @staticmethod
    def get_id_series(source_frame: pd.DataFrame) -> Tuple[pd.Series, pd.Series]:
        """Returns the ID series to be searched in the elasticsearch.

        :param source_frame: The source frame
        :return: Tuple of series
        """
        complete_name_series = (
            source_frame.loc[
                :,
                RefinitivTrEikonSourceColumns.COMPLIANCE_ACTIVITY_USERS_USER_INFO_FIRST_NAME.field,
            ]
            + " "
            + source_frame.loc[
                :, RefinitivTrEikonSourceColumns.COMPLIANCE_ACTIVITY_USERS_USER_INFO_LAST_NAME.field
            ]
        )
        identifiers_series = source_frame.loc[
            :, RefinitivTrEikonSourceColumns.COMPLIANCE_ACTIVITY_USERS_USER_INFO_IDENTIFIER.field
        ]
        return complete_name_series, identifiers_series

    @classmethod
    def identify_input_user_records_with_my_market_data(
        cls,
        complete_name_series: pd.Series,
        hits: pd.DataFrame,
        identifiers_series: pd.Series,
        model: Type[SteelEyeSchemaBaseModelES8],
    ) -> pd.Series:
        """The input series' identifiers exist in ElasticSearch. We need to
        find which, and drop them from the input DataFrame as we do not need to
        create MarketSuggestion records for it.

        :param complete_name_series: Series with input records complete name
         (first + last)
        :param hits: DataFrame with ElasticSearch query results
        :param identifiers_series: Series with input records identifier (email)
        :param model: MyMarket Schema model
        :return: Boolean mask Series -> True for input records that correspond to
         an existing MyMarket record,
        False otherwise
        """

        unique_hit_emails_list = set(
            hit
            for hit in hits.explode(TERMS_FIELD)[TERMS_FIELD].values.tolist()
            if not pd.isna(hit)
        )
        logger.info(
            f"Found {len(unique_hit_emails_list)} unique identifiers from MyMarket records for"
            f" the {model.schema()['title']} model"
        )

        if not unique_hit_emails_list:
            return pd.Series(False, index=complete_name_series.index)

        unique_hit_emails_regex = "|".join(unique_hit_emails_list)
        hit_by_name_mask = complete_name_series.str.contains(unique_hit_emails_regex)
        hit_by_identifier_mask = identifiers_series.str.contains(unique_hit_emails_regex)
        return hit_by_name_mask | hit_by_identifier_mask

    @classmethod
    def get_my_market_records_by_email(
        cls,
        es_client,
        model: Type[SteelEyeSchemaBaseModelES8],
        query_term_id_values: List[str],
        tenant: str,
    ) -> pd.DataFrame:
        """Scroll ElasticSearch to find MyMarket records based on the input
        user data identifiers.

        :param es_client: ElasticSearch Client
        :param model: MyMarket Schema Model to know which index to search in
         i.e. AccountPerson -> tenant:account_person
        :param query_term_id_values: List of identifiers to match against
         ElasticSearch records
        :param tenant: Tenant name
        :return: DataFrame with ElasticSearch scroll results. May be empty
         if there were no hits
        """
        tenant_model_es_alias = model.get_elastic_index_alias(tenant=tenant)
        query = get_terms_query(
            ids=query_term_id_values,
            es_client=es_client,
            lookup_field=TERMS_FIELD,
            model_field=model.schema()["title"],
            source_field=TERMS_FIELD,
        )
        return es_scroll(es_client=es_client, index_alias=tenant_model_es_alias, query=query)


def run_prepare_market_suggestion_df(
    list_of_user_dataframes: List[pd.DataFrame],
    tenant: str,
    app_metrics_path: Optional[str] = None,
    audit_path: Optional[str] = None,
) -> List[pd.DataFrame]:
    task = PrepareMarketSuggestionDf(app_metrics_path=app_metrics_path, audit_path=audit_path)
    return task.run(list_of_user_dataframes=list_of_user_dataframes, tenant=tenant)
