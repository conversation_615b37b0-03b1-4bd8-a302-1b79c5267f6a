import logging
import pandas as pd
from aries_se_comms_tasks.transcription.static import Transcript<PERSON><PERSON><PERSON><PERSON>ields
from aries_se_comms_tasks.voice.static import CallColumns, SemanticModelFields
from aries_se_core_tasks.core.integration_task import IntegrationTask
from typing import Optional

logger = logging.getLogger(__name__)


class TempColumns:
    CALL_RECORDING_SOURCE_KEY = "__call_recording_source_key__"


class CopilotCallFrameWithTranscriptFields(IntegrationTask):
    """Task which merges the call and transcript df by the recording source
    key, and returns a Call frame with required fields.

    It also adds Semantic and analytics fields from the transcript frame
    """

    def _run(
        self,
        call_frame: pd.DataFrame,
        transcript_frame_with_copilot: pd.DataFrame,
        cloud_provider_prefix: str,
        **kwargs,
    ) -> pd.DataFrame:
        required_call_columns = [
            CallColumns.VOICE_FILE_FILE_INFO_LOCATION_BUCKET,
            CallColumns.VOICE_FILE_FILE_INFO_LOCATION_KEY,
        ]
        if (
            call_frame.empty
            or any(element not in call_frame.columns for element in required_call_columns)
            or transcript_frame_with_copilot.empty
            or TranscriptModelFields.RECORDING_SOURCE_KEY
            not in transcript_frame_with_copilot.columns
        ):
            logger.warning(
                "Call frame/transcript frame empty or missing required fields."
                "Returning call frame without any changes"
            )
            return call_frame
        # Create temp column containing the entire source key
        # Use existing SOURCE_KEY if available, otherwise construct it manually
        if CallColumns.SOURCE_KEY in call_frame.columns:
            call_frame.loc[:, TempColumns.CALL_RECORDING_SOURCE_KEY] = call_frame.loc[:, CallColumns.SOURCE_KEY]
        else:
            # Fallback to manual construction if SOURCE_KEY is not available
            call_frame.loc[:, TempColumns.CALL_RECORDING_SOURCE_KEY] = (
                cloud_provider_prefix
                + call_frame.loc[:, CallColumns.VOICE_FILE_FILE_INFO_LOCATION_BUCKET]
                + "/"
                + call_frame.loc[:, CallColumns.VOICE_FILE_FILE_INFO_LOCATION_KEY]
            )

        transcript_fields_in_call_frame = [
            CallColumns.ANALYTICS_COPILOT_ANALYTICS_RISKS,
            CallColumns.ANALYTICS_COPILOT_ANALYTICS_METADATA,
            TranscriptModelFields.RECORDING_SOURCE_KEY,
        ] + SemanticModelFields.list()

        cols_mask = transcript_frame_with_copilot.columns.isin(transcript_fields_in_call_frame)
        transcript_fields_in_call_df = transcript_frame_with_copilot.loc[:, cols_mask]
        original_index = call_frame.index
        # Before merging, make sure that the Call frame does not already have
        # fields from
        final_call_df = pd.merge(
            left=call_frame.drop
            (columns=transcript_fields_in_call_frame,
            errors="ignore"),
            right=transcript_fields_in_call_df,
            left_on=TempColumns.CALL_RECORDING_SOURCE_KEY,
            right_on=TranscriptModelFields.RECORDING_SOURCE_KEY,
            how="left",
        )
        final_call_df.index = original_index
        # Drop the non-Call columns used in the pd.merge from the final_call_df
        final_call_df = final_call_df.drop(
            columns=[
                TempColumns.CALL_RECORDING_SOURCE_KEY,
                TranscriptModelFields.RECORDING_SOURCE_KEY,
            ]
        )
        return final_call_df


def run_copilot_call_frame_with_transcript_fields(
    call_frame: pd.DataFrame,
    transcript_frame_with_copilot: pd.DataFrame,
    cloud_provider_prefix: str,
    app_metrics_path: Optional[str] = None,
    audit_path: Optional[str] = None,
    **kwargs,
) -> pd.DataFrame:
    task = CopilotCallFrameWithTranscriptFields(
        app_metrics_path=app_metrics_path, audit_path=audit_path
    )

    return task.run(  # type: ignore[no-any-return]
        call_frame=call_frame,
        transcript_frame_with_copilot=transcript_frame_with_copilot,
        cloud_provider_prefix=cloud_provider_prefix,
        **kwargs,
    )
