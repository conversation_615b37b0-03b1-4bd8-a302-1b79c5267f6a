import boto3
import os
import pandas as pd
import pytest
from aries_se_comms_tasks.voice.split_and_resolve_metadata_and_recordings import (
    Params,
    run_split_and_resolve_metadata_and_recording,
)
from integration_test_utils.aws_helpers.patch_response import (
    mock_aiobotocore_convert_to_response_dict,
)
from integration_wrapper.static import IntegrationAriesTaskVariables
from moto import mock_aws
from pathlib import Path
from se_enums.cloud import CloudProviderEnum
from shutil import rmtree
from typing import List
from unittest.mock import patch

PATH = Path(__file__).parent
SERIALIZER_TMP_DIR = PATH.joinpath("serializer_tmp_dir")

SERIALIZER_TMP_DIR.mkdir(parents=True, exist_ok=True)

os.environ[IntegrationAriesTaskVariables.SERIALIZER_TMP_DIR] = SERIALIZER_TMP_DIR.as_posix()


@pytest.fixture(scope="session", autouse=True)
def cleanup(request):
    def _end():
        rmtree(SERIALIZER_TMP_DIR)

    request.addfinalizer(_end)


@pytest.fixture
def file_path_list() -> List[str]:
    return [
        "s3://test.dev.steeleye.co/aries/2023/11/07/file_1.wav",
        "s3://test.dev.steeleye.co/aries/2023/11/07/file_1.xml",
        "s3://test.dev.steeleye.co/aries/2023/11/07/file_2.wav",
        "s3://test.dev.steeleye.co/aries/2023/11/07/file_3.xml",
        "s3://test.dev.steeleye.co/aries/2023/11/13/file_4.xml",
    ]


@pytest.fixture
def expected_recording_df() -> pd.DataFrame:
    return pd.DataFrame(
        {
            "recording_column": [
                "s3://test.dev.steeleye.co/aries/2023/11/07/file_1.wav",
                "s3://test.dev.steeleye.co/aries/2023/11/07/file_2.wav",
                "s3://test.dev.steeleye.co/aries/2023/11/12/file_3.wav",
                pd.NA,
            ]
        }
    )


@pytest.fixture
def expected_metadata_df() -> pd.DataFrame:
    return pd.DataFrame(
        {
            "metadata_column": [
                "s3://test.dev.steeleye.co/aries/2023/11/07/file_1.xml",
                "s3://test.dev.steeleye.co/aries/2023/11/11/file_2.xml",
                "s3://test.dev.steeleye.co/aries/2023/11/07/file_3.xml",
                "s3://test.dev.steeleye.co/aries/2023/11/13/file_4.xml",
            ],
        }
    )


mock_aiobotocore_convert_to_response_dict()


@patch(
    "aries_se_comms_tasks.voice.split_and_resolve_metadata_and_recordings.resolve_missing_paths_in_aws",
    return_value={
        "s3://test.dev.steeleye.co/aries/2023/11/07/file_2.xml": "s3://test.dev.steeleye.co/aries/2023/11/11/file_2.xml",
        "s3://test.dev.steeleye.co/aries/2023/11/07/file_3.wav": "s3://test.dev.steeleye.co/aries/2023/11/12/file_3.wav",
    },
)
def test_end_to_end_execution_with_historical_resolution(
    mock_resolve_paths, file_path_list, expected_metadata_df, expected_recording_df
):
    params = Params(
        metadata_extension=".xml",
        recording_extension=".wav",
        target_metadata_col_name="metadata_column",
        target_recording_col_name="recording_column",
        time_duration_to_resolve_missing_files=7,
    )

    result_df = run_split_and_resolve_metadata_and_recording(
        file_path_list=file_path_list,
        params=params,
        skip_serializer=True,
    )

    expected_df = pd.concat([expected_recording_df, expected_metadata_df], axis=1)

    pd.testing.assert_frame_equal(result_df, expected_df)


@patch(
    "aries_se_comms_tasks.voice.split_and_resolve_metadata_and_recordings.resolve_missing_paths_in_aws",
    return_value={},
)
def test_end_to_end_execution_without_historical_resolution(
    mock_resolve_paths, file_path_list, expected_metadata_df, expected_recording_df
):
    file_path_list = file_path_list[0:2]
    params = Params(
        metadata_extension=".xml",
        recording_extension=".wav",
        target_metadata_col_name="metadata_column",
        target_recording_col_name="recording_column",
        time_duration_to_resolve_missing_files=7,
    )

    result_df = run_split_and_resolve_metadata_and_recording(
        file_path_list=file_path_list,
        params=params,
        skip_serializer=True,
    )

    expected_df = pd.concat([expected_recording_df, expected_metadata_df], axis=1)

    pd.testing.assert_frame_equal(result_df, expected_df.head(1))


@patch(
    "aries_se_comms_tasks.voice.split_and_resolve_metadata_and_recordings.resolve_missing_paths_in_aws",
    return_value={
        "s3://test.dev.steeleye.co/aries/2023/11/07/file_2.xml": "s3://test.dev.steeleye.co/aries/2023/11/11/file_2.xml",
        "s3://test.dev.steeleye.co/aries/2023/11/07/file_3.wav": "s3://test.dev.steeleye.co/aries/2023/11/12/file_3.wav",
    },
)
def test_end_to_end_execution_with_df_split(
    mock_resolve_paths, file_path_list, expected_metadata_df, expected_recording_df
):
    params = Params(
        metadata_extension=".xml",
        recording_extension=".wav",
        target_metadata_col_name="metadata_column",
        target_recording_col_name="recording_column",
        time_duration_to_resolve_missing_files=7,
        split_meta_data_and_call_df=True,
    )

    result_metadata_df, result_recording_df = run_split_and_resolve_metadata_and_recording(
        file_path_list=file_path_list,
        params=params,
        skip_serializer=True,
    )

    pd.testing.assert_frame_equal(result_metadata_df, expected_metadata_df)
    pd.testing.assert_frame_equal(result_recording_df, expected_recording_df)


@mock_aws
@patch(
    "aries_se_comms_tasks.voice.split_and_resolve_metadata_and_recordings.get_cloud_provider_from_file_uri",
    return_value=CloudProviderEnum.AZURE,
)
@patch(
    "aries_se_comms_tasks.voice.split_and_resolve_metadata_and_recordings.get_cloud_provider_prefix",
    return_value="s3://",
)
def test_end_to_end_execution_for_cloud_provider_other_than_aws(
    mock_cloud_provider_prefix,
    mock_cloud_provider,
    file_path_list,
    expected_metadata_df,
    expected_recording_df,
):
    """Triggers the generic cloud provider code flow but internally uses AWS
    fsspec file system."""
    params = Params(
        metadata_extension=".xml",
        recording_extension=".wav",
        target_metadata_col_name="metadata_column",
        target_recording_col_name="recording_column",
        time_duration_to_resolve_missing_files=7,
    )

    # Setup mock S3 bucket with the desired contents
    s3 = boto3.client("s3", region_name="us-east-1")
    s3.create_bucket(Bucket="test.dev.steeleye.co")
    s3.put_object(
        Bucket="test.dev.steeleye.co",
        Key="aries/2023/11/11/file_2.xml",
    )
    s3.put_object(Bucket="test.dev.steeleye.co", Key="aries/2023/11/12/file_3.wav")

    result_df = run_split_and_resolve_metadata_and_recording(
        file_path_list=file_path_list,
        params=params,
        skip_serializer=True,
    )

    expected_df = pd.concat([expected_recording_df, expected_metadata_df], axis=1)

    pd.testing.assert_frame_equal(result_df, expected_df)
