import pandas as pd
import pytest


@pytest.fixture()
def source_frame() -> pd.DataFrame:
    return pd.DataFrame(
        data={
            "from": [
                "+442345678901",
                "<EMAIL>",
            ],
            "to": [
                "<EMAIL>",
                "+441234567890",
            ],
            "from info": [
                pd.NA,
                "Person 1",
            ],
            "to info": [
                pd.NA,
                "+441234567890",
            ],
            "start datetime": [
                "2023.04.17 12:51:20.370",
                "2023.04.14 13:42:47.923",
            ],
            "end datetime": [
                "2023.04.17 12:54:15.720",
                "2023.04.14 13:46:59.770",
            ],
            "duration": [
                "00:02:55",
                "00:04:12",
            ],
            "direction": [
                "PSTN In",
                "PSTN Out",
            ],
            "metadata_file_url": [
                "s3://test.dev.steeleye.co/aries/nonstreamed/evented/2b0890cc-dd16-11ed-a94a-001dd8b71dff_(20230417115120369).csv",  # noqa: E501
                "s3://test.dev.steeleye.co/aries/nonstreamed/evented/9e85c138-23c8-45f9-9ce1-552eefcf582a_(20230414134247923).csv",  # noqa: E501
            ],
        }
    )


@pytest.fixture()
def expected_result() -> pd.DataFrame:
    return pd.DataFrame(
        data=[
            {
                "callDuration": "00:02:55",
                "connected": True,
                "fault": "PSTN In",
                "id": "2b0890cc-dd16-11ed-a94a-001dd8b71dff_(20230417115120369)",
                "internal": False,
                "identifiers.allCountryCodes": ["GB"],
                "identifiers.allIds": ["+442345678901", "<EMAIL>"],
                "identifiers.fromId": "+442345678901",
                "identifiers.fromIdAddlInfo": {"raw": "+442345678901", "countryCode": "GB"},
                "identifiers.toIds": ["<EMAIL>"],
                "identifiers.toIdsAddlInfo": [
                    {"raw": "<EMAIL>", "countryCode": None}
                ],
                "metadata.source.client": "VIA",
                "metadata.source.fileInfo.location.bucket": "test.dev.steeleye.co",
                "metadata.source.fileInfo.location.key": "aries/nonstreamed/evented/2b0890cc-dd16-11ed-a94a-001dd8b71dff_(20230417115120369).csv",  # noqa: E501
                "sourceIndex": 0,
                "sourceKey": "s3://test.dev.steeleye.co/aries/ingest_batching/via_voice/2024/05/30/test_data.ndjson",
                "timestamps.localTimestampStart": "2023-04-17T12:51:20.370000Z",
                "timestamps.localTimestampEnd": "2023-04-17T12:54:15.720000Z",
                "timestamps.timestampStart": "2023-04-17T12:51:20.370000Z",
                "timestamps.timestampConnected": "2023-04-17T12:51:20.370000Z",
                "timestamps.timestampEnd": "2023-04-17T12:54:15.720000Z",
                "waveform.location.bucket": "test.dev.steeleye.co",
                "waveform.location.key": "attachments/waveform/via_voice_transform/2b0890cc-dd16-11ed-a94a-001dd8b71dff_(20230417115120369)/waveform.json",  # noqa: E501
                "__attachment_path__": "s3://test.dev.steeleye.co/aries/nonstreamed/evented/2b0890cc-dd16-11ed-a94a-001dd8b71dff_(20230417115120369).wav",  # noqa: E501
            },
            {
                "callDuration": "00:04:12",
                "connected": True,
                "fault": "PSTN Out",
                "id": "9e85c138-23c8-45f9-9ce1-552eefcf582a_(20230414134247923)",
                "internal": False,
                "identifiers.allCountryCodes": ["GB"],
                "identifiers.allIds": ["+441234567890", "<EMAIL>"],
                "identifiers.fromId": "<EMAIL>",
                "identifiers.fromIdAddlInfo": {"raw": "<EMAIL>", "countryCode": None},
                "identifiers.toIds": ["+441234567890"],
                "identifiers.toIdsAddlInfo": [{"raw": "+441234567890", "countryCode": "GB"}],
                "metadata.source.client": "VIA",
                "metadata.source.fileInfo.location.bucket": "test.dev.steeleye.co",
                "metadata.source.fileInfo.location.key": "aries/nonstreamed/evented/9e85c138-23c8-45f9-9ce1-552eefcf582a_(20230414134247923).csv",  # noqa: E501
                "sourceIndex": 1,
                "sourceKey": "s3://test.dev.steeleye.co/aries/ingest_batching/via_voice/2024/05/30/test_data.ndjson",
                "timestamps.localTimestampStart": "2023-04-14T13:42:47.923000Z",
                "timestamps.localTimestampEnd": "2023-04-14T13:46:59.770000Z",
                "timestamps.timestampStart": "2023-04-14T13:42:47.923000Z",
                "timestamps.timestampConnected": "2023-04-14T13:42:47.923000Z",
                "timestamps.timestampEnd": "2023-04-14T13:46:59.770000Z",
                "waveform.location.bucket": "test.dev.steeleye.co",
                "waveform.location.key": "attachments/waveform/via_voice_transform/9e85c138-23c8-45f9-9ce1-552eefcf582a_(20230414134247923)/waveform.json",  # noqa: E501
                "__attachment_path__": "s3://test.dev.steeleye.co/aries/nonstreamed/evented/9e85c138-23c8-45f9-9ce1-552eefcf582a_(20230414134247923).wav",  # noqa: E501
            },
        ]
    )
