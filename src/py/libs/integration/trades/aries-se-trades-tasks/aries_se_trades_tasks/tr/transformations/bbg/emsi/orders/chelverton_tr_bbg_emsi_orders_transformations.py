import pandas as pd
from aries_se_core_tasks.transform.map.map_conditional import (  # type: ignore[attr-defined]
    Params as MapConditionalParams,
)
from aries_se_core_tasks.transform.map.map_conditional import (  # type: ignore[attr-defined]
    run_map_conditional,
)
from aries_se_trades_tasks.tr.transformations.bbg.emsi.orders.static import (
    SourceColumns,
    TempColumns,
)
from aries_se_trades_tasks.tr.transformations.bbg.emsi.orders.tr_bbg_emsi_orders_transformations import (  # noqa: E501
    TrBBGEMSIOrdersTransformations,
)
from se_core_tasks.map.map_conditional import Case
from se_trades_tasks.tr.static import RTS22TransactionColumns


class ChelvertonTrBBGEMSIOrdersTransformations(TrBBGEMSIOrdersTransformations):
    def _data_source_name(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=["Chelverton EMSI"] * self.source_frame.shape[0],
            index=self.source_frame.index,
            columns=[RTS22TransactionColumns.DATA_SOURCE_NAME],
        )

    def get_parties_trader(self) -> pd.DataFrame:
        """Map as follows:

        If not Trader Name: Trader UUID
        elif trader notes trader in [CUEG, DUG, TIC]
            and Trader Name not in [Ed Booth, Henry Botting]: James Baker
        elif trader notes trader is CEIF and Trader Name is not Oli Knott: David Taylor
        elif trader notes trader is UKDT and Trader Name is not Oli Knott: David Horner
        elif trader notes trader is CESF and Trader Name is not Gareth Rudd: Dale Robertson
        elif trader notes trader in [GLTH, CSCS] and Trader Name is not Harold Thompson: Mark Purdy
        elif trader notes trader in [CHOP] and Trader Name is not Henry Botting: Julie Dean
        else: Trader Name
        """
        custom_trader = pd.Series(
            data=[pd.NA] * self.source_frame.shape[0],
            index=self.source_frame.index,
            name=TempColumns.CUSTOM_TRADER,
        )

        not_ed_booth_henry_botting = ~(
            self.source_frame.loc[:, SourceColumns.TRADER_NAME]
            .astype(str)
            .str.fullmatch("Ed.* Booth|Henry Botting", case=False, na=False)
        )
        not_oli_knott = ~(
            self.source_frame.loc[:, SourceColumns.TRADER_NAME]
            .astype(str)
            .str.fullmatch("Oli.* Knott", case=False, na=False)
        )
        not_gareth_rudd = ~(
            self.source_frame.loc[:, SourceColumns.TRADER_NAME]
            .astype(str)
            .str.fullmatch("Gareth Rudd", case=False, na=False)
        )
        not_harold_thompson = ~(
            self.source_frame.loc[:, SourceColumns.TRADER_NAME]
            .astype(str)
            .str.fullmatch("Harold Thompson", case=False, na=False)
        )
        trader_notes_cueg_dug_tic = (
            self.pre_process_df.loc[:, TempColumns.TRADER_NOTES_TRADER]
            .astype(str)
            .str.fullmatch("CUEG|DUG|TIC", case=False, na=False)
        )
        trader_notes_ceif = (
            self.pre_process_df.loc[:, TempColumns.TRADER_NOTES_TRADER]
            .astype(str)
            .str.fullmatch("CEIF", case=False, na=False)
        )
        trader_notes_ukdt = (
            self.pre_process_df.loc[:, TempColumns.TRADER_NOTES_TRADER]
            .astype(str)
            .str.fullmatch("UKDT", case=False, na=False)
        )
        trader_notes_cesf = (
            self.pre_process_df.loc[:, TempColumns.TRADER_NOTES_TRADER]
            .astype(str)
            .str.fullmatch("CESF", case=False, na=False)
        )
        trader_notes_glth_or_cscs = (
            self.pre_process_df.loc[:, TempColumns.TRADER_NOTES_TRADER]
            .astype(str)
            .str.fullmatch("CSCS|GLTH", case=False, na=False)
        )
        trader_notes_chop = (
            self.pre_process_df.loc[:, TempColumns.TRADER_NOTES_TRADER]
            .astype(str)
            .str.fullmatch("CHOP", case=False, na=False)
        )
        not_henry_botting = ~(
            self.source_frame.loc[:, SourceColumns.TRADER_NAME]
            .astype(str)
            .str.fullmatch("Henry Botting", case=False, na=False)
        )

        james_baker_mask = trader_notes_cueg_dug_tic & not_ed_booth_henry_botting
        david_taylor_mask = trader_notes_ceif & not_oli_knott
        david_horner_mask = trader_notes_ukdt & not_oli_knott
        dale_robertson_mask = trader_notes_cesf & not_gareth_rudd
        mark_purdy_mask = trader_notes_glth_or_cscs & not_harold_thompson
        julie_dean_mask = trader_notes_chop & not_henry_botting

        custom_trader[james_baker_mask] = "James Baker"
        custom_trader[david_taylor_mask] = "David Taylor"
        custom_trader[david_horner_mask] = "David Horner"
        custom_trader[dale_robertson_mask] = "Dale Robertson"
        custom_trader[mark_purdy_mask] = "Mark Purdy"
        custom_trader[julie_dean_mask] = "Julie Dean"

        return run_map_conditional(  # type: ignore[no-any-return]
            source_frame=pd.concat(
                [
                    custom_trader,
                    self.source_frame.loc[
                        :, [SourceColumns.TRADER_UUID, SourceColumns.TRADER_NAME]
                    ],
                ],
                axis=1,
            ),
            params=MapConditionalParams(
                target_attribute=TempColumns.TRADER,
                cases=[
                    Case(
                        query=f"`{SourceColumns.TRADER_NAME}`.isnull()",
                        attribute=SourceColumns.TRADER_UUID,
                    ),
                    Case(
                        query=f"`{TempColumns.CUSTOM_TRADER}`.notnull()",
                        attribute=TempColumns.CUSTOM_TRADER,
                    ),
                    Case(
                        query=f"`{SourceColumns.TRADER_NAME}`.notnull() & `{TempColumns.CUSTOM_TRADER}`.isnull()",  # noqa: E501
                        attribute=SourceColumns.TRADER_NAME,
                    ),
                ],
            ),
            skip_serializer=True,
        )
