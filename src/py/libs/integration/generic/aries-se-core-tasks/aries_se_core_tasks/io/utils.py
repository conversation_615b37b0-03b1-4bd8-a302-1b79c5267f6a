import boto3
import datetime
import fsspec
import logging
import numpy as np
import os
import pandas as pd
from abc import ABC
from aries_se_core_tasks.globals.cloud import STORAGE_GET_RATE_LIMIT
from aries_se_core_tasks.utilities.cloud import get_cloud_provider_from_file_path
from chardet import UniversalDetector
from contextlib import contextmanager
from math import ceil
from pandas import Timestamp
from pathlib import Path
from pydantic import BaseModel, Field
from typing import Any, Dict, Generator, List, Optional
from urllib.parse import urlparse

logger_ = logging.getLogger(__name__)


class FileExistsColumns:
    FILE_URL: str = "__file_url__"
    FILE_EXISTS: str = "__file_exists__"


class FileInfo(BaseModel, ABC):
    last_modified: datetime.datetime = Field(default=..., description="File last modified date")

    name: str = Field(default=..., description="File name")

    size: int = Field(default=..., description="File size in bytes")

    type: str = Field(default=..., description="File type")

    content_type: Optional[str] = Field(default=None, description="File content type")

    e_tag: Optional[str] = Field(default=None, description="File E tag")

    metadata: Optional[Dict[str, str]] = Field(default={}, description="File metadata")

    storage_class: Optional[str] = Field(default=None, description="File storage class")

    version_id: Optional[str] = Field(default=None, description="File version id")


def read_file(path: str) -> bytes:
    """Gets and returns a given file as bytes. It works for both local and
    remote files.

    :param path: the file path to get bytes
    :return: the file content as bytes
    """
    cloud_provider: str = get_cloud_provider_from_file_path(file_url=path)

    fs = fsspec.filesystem(cloud_provider)

    with fs.open(path, mode="rb") as f:
        return f.read()  # type: ignore[no-any-return]


def get_file_info(path: str, timezone: datetime.timezone = datetime.timezone.utc) -> FileInfo:
    """Gets and returns a given file info synchronously. It works for both
    local and remote files.

    :param path: the file path to get bytes
    :param timezone: timezone is used on local files conversion
    :return: the file info
    """
    # It will always return a `FileInfo` object, as `return_none_file_not_found` is `False`
    # by default. An exception will be raised if the file is not found.
    cloud_provider: str = get_cloud_provider_from_file_path(file_url=path)

    fs = fsspec.filesystem(cloud_provider)

    info: Dict[str, Any]

    info = fs.info(path=path)

    last_modified: datetime.datetime
    e_tag: str | None = None
    metadata: dict = {}

    if cloud_provider == "s3":
        last_modified = info["LastModified"]
        metadata = info.get("Metadata", {})
        e_tag = info.get("ETag")
    elif cloud_provider == "az":
        last_modified = info["last_modified"]
        metadata = info.get("metadata", {})
        e_tag = info.get("etag")
    else:
        # from any other cloud provider or local
        last_modified = datetime.datetime.fromtimestamp(info["mtime"], timezone)

    return FileInfo(
        name=info["name"],
        size=info["size"],
        type=info["type"],
        last_modified=last_modified,
        content_type=info.get("ContentType"),
        version_id=info.get("VersionId"),
        metadata=metadata,
        e_tag=e_tag,
        storage_class=info.get("StorageClass"),
    )


def get_files_info(
    lst: List[str],
    timezone: datetime.timezone = datetime.timezone.utc,
    chunk_size: Optional[int] = None,
    return_none_file_not_found: Optional[bool] = False,
) -> Generator[List[Optional[FileInfo]], None, None]:
    """This helper gets the info of a given files list from a given list
    through chunks, and yields the chunks.

    Do not that if a given list contains duplicate file paths, this
    helper will not differentiate them, and will make two requests
    to get the info of the same file.

    :param lst: the list of file urls for files to get their info
    :param timezone: timezone is used on local files conversion
    :param chunk_size: file size vary, this will be used essentially as a rate limiter.
                        default is: `STORAGE_GET_RATE_LIMIT`
    :param return_none_file_not_found: if `True`, it will return `None` for files that
    :yield: a list, this is can be a chunk or all of the content of `files_list`,
            depending on its size
    """
    total_files: int = len(lst)

    # Chunk size must always be within the allowed limits
    # Division is required because `array_split` expects
    # an exact value of expected chunks
    if chunk_size and 0 < chunk_size < STORAGE_GET_RATE_LIMIT:
        chunk_size = ceil(total_files / chunk_size)
    else:
        chunk_size = ceil(total_files / STORAGE_GET_RATE_LIMIT)

    # This chunking is required, to prevent being
    # caught by cloud storage rate limit
    lst: List[List[str]] = np.array_split(  # type: ignore[no-redef]
        ary=lst, indices_or_sections=chunk_size
    )

    def _get_file_info(file_path: str) -> Optional[FileInfo]:
        """Simple wrapper around `get_file_info` that adds the ability to
        return none a when a given file is not found.

        This is useful when we want to keep the order of the files.
        """
        try:
            return get_file_info(path=file_path, timezone=timezone)
        except FileNotFoundError as e:
            message = f"File {file_path} not found: {e}"
            if return_none_file_not_found:
                logger_.warning(message)
                return None

            raise FileNotFoundError(message)

    for chunk in lst:
        files_info_list: List[Optional[FileInfo]] = [
            _get_file_info(file_path=file_path) for file_path in chunk
        ]

        yield files_info_list


def check_file_exists(path: str) -> bool:
    """Checks if a given file exists. It works for both local and remote files.

    :param path: the path to check
    :return: `True` if file exists.
    """
    cloud_provider: str = get_cloud_provider_from_file_path(file_url=path)

    fs = fsspec.filesystem(cloud_provider)

    exists: bool = fs.exists(path=path)

    return exists


def check_files_list_exists(
    lst: List[str],
    chunk_size: Optional[int] = None,
) -> Generator[pd.DataFrame, None, None]:
    """This helper check if a given files list exists from a given list through
    chunks, and yields the chunks.

    :param lst: the list of file urls for files to check their existence
    :param chunk_size: file size vary, this will be used essentially as a rate limiter.
                        default is: `STORAGE_GET_RATE_LIMIT`
    :yield: a dataframe, this is can be a chunk or all of the content of `files_list`,
            depending on its size
    """
    total_files: int = len(lst)

    # Chunk size must always be within the allowed limits
    # Division is required because `array_split` expects
    # an exact value of expected chunks
    if chunk_size and 0 < chunk_size < STORAGE_GET_RATE_LIMIT:
        chunk_size = ceil(total_files / chunk_size)
    else:
        chunk_size = ceil(total_files / STORAGE_GET_RATE_LIMIT)

    # This chunking is required, to prevent being
    # caught by cloud storage rate limit
    lst: List[List[str]] = np.array_split(  # type: ignore[no-redef]
        ary=lst, indices_or_sections=chunk_size
    )

    for files_chunk in lst:
        chunk_df: pd.DataFrame = pd.DataFrame(
            data=files_chunk, columns=[FileExistsColumns.FILE_URL]
        )

        files_checked_list: List[Dict[int, bool]] = [
            {index: check_file_exists(path=file_path)}
            for index, file_path in enumerate(files_chunk)
        ]

        # Associates `FileExistsColumns.FILE_URL` with `FileExistsColumns.FILE_EXISTS`
        chunk_df = pd.concat(
            [
                chunk_df,
                pd.Series(
                    data={
                        index: exists
                        for file_dict in files_checked_list
                        if type(file_dict) in (dict, Dict)
                        for index, exists in file_dict.items()
                    },
                    name=FileExistsColumns.FILE_EXISTS,
                ),
            ],
            axis=1,
        )

        yield chunk_df


def resolve_missing_paths_in_aws(
    file_prefix: str,
    bucket: str,
    from_date: datetime.datetime | Timestamp,
    to_date: datetime.datetime | Timestamp,
    to_be_resolved_file_path_list: List[str],
) -> Dict[str, str]:
    """Resolve input file paths with the actual file paths present in AWS S3.
    This resolution is done within a specified date range.

    :param file_prefix: The prefix common to the files in the S3 bucket.
    :param bucket: The name of the AWS S3 bucket.
    :param from_date: The start date of the date range.
    :param to_date: The end date of the date range.
    :param to_be_resolved_file_path_list: List of file paths to be resolved.

    :return: A dictionary mapping original file paths to their resolved S3 paths.
    Paths which could not be resolved are not present in the output.
    """
    s3 = boto3.client("s3")
    paginator = s3.get_paginator("list_objects_v2")
    to_be_resolved_file_name_list = [Path(path).name for path in to_be_resolved_file_path_list]

    # This is assuming that the difference between the min and max dates isn't too large
    # If it is then we would make greater number of calls to S3 to resolve missing files.
    file_resolution_dict = {}
    for date in pd.date_range(from_date, to_date):
        pages = paginator.paginate(
            Bucket=bucket, Prefix=f"{file_prefix}{date.strftime(format='%Y/%m/%d')}/"
        )
        for page in pages:
            for s3_obj in page.get("Contents", []):
                s3_path = s3_obj.get("Key")
                if not s3_path:
                    continue

                s3_path_name = Path(s3_path).name
                if s3_path_name in to_be_resolved_file_name_list:
                    file_resolution_dict[
                        to_be_resolved_file_path_list[
                            to_be_resolved_file_name_list.index(s3_path_name)
                        ]
                    ] = "s3://" + str(Path(page.get("Name", "")).joinpath(s3_path))

    return file_resolution_dict


@contextmanager
def temporarily_unset_env_vars(env_vars: List[str]):
    """Temporarily unsets the specified environment variables and yields
    control back to the code which runs the code without the specified env
    variables (the code inside the context manager). As soon as the code inside
    the context manager has run completely, the env vars are reset.

    :param env_vars: List of env vars to temporarily unset and
    reset
    """
    # Save the current values of the environment variables
    original_env_vars = {var: os.environ.pop(var, None) for var in env_vars}

    try:
        # Execute the block of code where the variables are unset
        yield
    finally:
        # Restore the environment variables to their original state
        for var, value in original_env_vars.items():
            if value is not None:
                os.environ[var] = value


def fsspec_read_with_azure_sas_token(
    azure_http_url: str,
    storage_account_name: str,
    sas_token: str,
    blob_container_name: str,
    write_directory: Path,
) -> str | None:
    """Cloud-specific code which uses FSSPEC to read a file in an Azure Blob
    Container using the Azure storage account name and SAS token. It writes the
    bytes content of the file if it was able to read the file to a local file
    and returns it. If there is an exception, it returns None. The
    'write_directory' is expected to have been created before calling the
    function.

    E.g. of Azure HTTP URL: https://ingluwareprodeuwestorage.blob.core.windows.net/2023/09/25/abc.att

    :param azure_http_url: Azure HTTP URL WITHOUT the blob container name
    :param storage_account_name: Azure Storage Account name
    :param sas_token: Azure SAS token
    :param blob_container_name: Azure Blob Container name
    :param write_directory: Directory to which the bytes content of the attachment is written
    :returns: Bytes content of attachment file
    """
    parsed_url = urlparse(azure_http_url)
    blob_name = parsed_url.path.lstrip("/")

    filename = Path(blob_name).name
    az_url = f"az://{blob_container_name.rstrip('/')}/{blob_name}"
    fs = fsspec.filesystem("az", account_name=storage_account_name, sas_token=sas_token)
    try:
        with fs.open(az_url, "rb") as infile:
            output_filename = write_directory.joinpath(filename)
            with open(output_filename, "wb") as outfile:
                outfile.write(infile.read())
            return output_filename.as_posix()
    except Exception as e:
        logger_.warning(
            f"Something went wrong while downloading attachment {azure_http_url}. Exception: {e}"
        )
        return  # type: ignore[return-value]


def get_encoding_from_file(csv_path: Path, default_encoding: str = "utf-8") -> str:
    """Reads the file in chunks and detects the encoding for each. Outputs the
    most common detected encoding.

    :param csv_path: file path
    :param default_encoding: default encoding
    :return: encoding
    """

    with fsspec.open(csv_path.as_posix(), mode="rb") as csv_file:
        # implemented according to https://chardet.readthedocs.io/en/latest/usage.html#advanced-usage

        detector = UniversalDetector()
        for line in csv_file.readlines():
            detector.feed(line)
            if detector.done:
                break
        detector.close()

    chardet_result = detector.result.get("encoding")

    if chardet_result:
        return chardet_result

    return default_encoding
