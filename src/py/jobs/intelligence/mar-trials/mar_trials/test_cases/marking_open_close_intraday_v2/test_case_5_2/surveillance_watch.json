{"&hash": "e33791216c404c0489d03be26ac383b3d89a7d7cd2ec3d8998bd18da36dc2c38", "&id": "64b82084-9598-89a9-8794-387028b7b69a", "&key": "SurveillanceWatch:64b82084-9598-89a9-8794-387028b7b69a:1713274177983", "&model": "SurveillanceWatch", "&timestamp": "1704067200000", "&user": "<EMAIL>", "&version": 1, "name": "marking_open_close_intraday_v2___test_case_5_2", "queryType": "MARKET_ABUSE", "query": {"thresholds": "{\"runType\": \"Executions\", \"behaviourType\": \"Marking the Close\", \"directionality\": \"Only Buys, or Only Sells\", \"evaluationType\": \"Trader\", \"marketComparison\": 0, \"priceImprovement\": 0, \"timeWindow\": {\"unit\": \"seconds\", \"value\": 1800}}", "marketAbuseReportType": "MARKING_OPEN_CLOSE_INTRADAY_V2", "name": "test_case_5_2", "filters": "{\"bool\":{\"must\":{\"terms\":{\"sourceKey\":[\"steeleyeBlotter.mar.mtcmto.5.4.csv\"]}}}}"}, "createdOn": "2024-01-01T00:00:00.000000+00:00", "frequencyType": "DAILY", "status": "ACTIVE", "backtest": "FULL", "createdBy": "mar.trials", "createdByAdmin": true, "scheduleDetails": {"timeOfDay": "13:00", "timeZone": "Europe/Lisbon", "recurrence": "DAILY"}}