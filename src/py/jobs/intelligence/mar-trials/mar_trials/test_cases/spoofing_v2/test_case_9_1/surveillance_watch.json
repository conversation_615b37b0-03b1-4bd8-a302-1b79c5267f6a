{"&hash": "e33791216c404c0489d03be26ac383b3d89a7d7cd2ec3d8998bd18da36dc2c38", "&id": "f5825d14-83b3-4ee3-2ee9-5d10543639c5", "&key": "SurveillanceWatch:f5825d14-83b3-4ee3-2ee9-5d10543639c5:1713274177983", "&model": "SurveillanceWatch", "&timestamp": "1704067200000", "&user": "<EMAIL>", "&version": 1, "name": "spoofing_v2___test_case_9_1", "queryType": "MARKET_ABUSE", "query": {"thresholds": "{\"evaluationType\": \"executingEntity\", \"includePartCancellations\": true, \"realOrderPercentageFilled\": 1, \"spoofOrderPercentageLevel\": 0.01, \"spoofOrderTimeToCancel\": 6, \"spoofingTimeWindow\": 10, \"priceImprovement\": false}", "marketAbuseReportType": "SPOOFING_V2", "name": "test_case_9_1", "filters": "{\"bool\":{\"must\":{\"terms\":{\"sourceKey\":[\"steeleyeblotter.mar.spoofingv2.9.csv\"]}}}}"}, "createdOn": "2024-01-01T00:00:00.000000+00:00", "frequencyType": "DAILY", "status": "ACTIVE", "backtest": "FULL", "createdBy": "mar.trials", "createdByAdmin": true, "scheduleDetails": {"timeOfDay": "13:00", "timeZone": "Europe/Lisbon", "recurrence": "DAILY"}}