{"&hash": "e33791216c404c0489d03be26ac383b3d89a7d7cd2ec3d8998bd18da36dc2c38", "&id": "68783c22-9d59-7d75-6ff0-f264067896ca", "&key": "SurveillanceWatch:68783c22-9d59-7d75-6ff0-f264067896ca:1713274177983", "&model": "SurveillanceWatch", "&timestamp": "1704067200000", "&user": "<EMAIL>", "&version": 1, "name": "potam___test_case_12_1", "queryType": "MARKET_ABUSE", "query": {"thresholds": "{\"extendedWindowDaysBefore\": 0, \"extendedWindowDaysAfter\": 0}", "marketAbuseReportType": "POTAM", "name": "test_case_12_1", "filters": "{\"bool\":{\"must\":{\"terms\":{\"sourceKey\":[\"steeleyeblotter.mar.potam.12.csv\"]}}}}"}, "createdOn": "2024-01-01T00:00:00.000000+00:00", "frequencyType": "DAILY", "status": "ACTIVE", "backtest": "FULL", "createdBy": "mar.trials", "createdByAdmin": true, "scheduleDetails": {"timeOfDay": "13:00", "timeZone": "Europe/Lisbon", "recurrence": "DAILY"}}