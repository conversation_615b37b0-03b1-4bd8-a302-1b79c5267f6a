{"&hash": "e33791216c404c0489d03be26ac383b3d89a7d7cd2ec3d8998bd18da36dc2c38", "&id": "cc50db7f-8625-3ae4-5c84-31b712c5a05c", "&key": "SurveillanceWatch:cc50db7f-8625-3ae4-5c84-31b712c5a05c:1713274177983", "&model": "SurveillanceWatch", "&timestamp": "1704067200000", "&user": "<EMAIL>", "&version": 1, "name": "potam___test_case_10_2", "queryType": "MARKET_ABUSE", "query": {"thresholds": "{\"extendedWindowDaysBefore\": 2, \"extendedWindowDaysAfter\": 5}", "marketAbuseReportType": "POTAM", "name": "test_case_10_2", "filters": "{\"bool\":{\"must\":{\"terms\":{\"sourceKey\":[\"steeleyeblotter.mar.potam.10.csv\"]}}}}"}, "createdOn": "2024-01-01T00:00:00.000000+00:00", "frequencyType": "DAILY", "status": "ACTIVE", "backtest": "FULL", "createdBy": "mar.trials", "createdByAdmin": true, "scheduleDetails": {"timeOfDay": "13:00", "timeZone": "Europe/Lisbon", "recurrence": "DAILY"}}