{"&hash": "e33791216c404c0489d03be26ac383b3d89a7d7cd2ec3d8998bd18da36dc2c38", "&id": "f38d0bca-d7e2-99da-a261-62130f99ca3a", "&key": "SurveillanceWatch:f38d0bca-d7e2-99da-a261-62130f99ca3a:1713274177983", "&model": "SurveillanceWatch", "&timestamp": "1704067200000", "&user": "<EMAIL>", "&version": 1, "name": "marking_the_open___test_case_2_4", "queryType": "MARKET_ABUSE", "query": {"thresholds": "{\"client20DayAdv\": 0, \"lookBackPeriod\": 7200, \"market20DayAdv\": 0.02, \"markingType\": \"Marking the open\", \"minimumNotionalCurrency\": \"GBP\", \"priceSpike\": 0.02, \"minimumNotional\": 0}", "marketAbuseReportType": "MARKING_THE_OPEN", "name": "test_case_2_4", "filters": "{\"bool\":{\"must\":{\"terms\":{\"sourceKey\":[\"steeleyeblotter.mar.markingtheopen.2.2.csv\"]}}}}"}, "createdOn": "2024-01-01T00:00:00.000000+00:00", "frequencyType": "DAILY", "status": "ACTIVE", "backtest": "FULL", "createdBy": "mar.trials", "createdByAdmin": true, "scheduleDetails": {"timeOfDay": "13:00", "timeZone": "Europe/Lisbon", "recurrence": "DAILY"}}